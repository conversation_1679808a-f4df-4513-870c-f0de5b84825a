{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ChattrixFrontEnd": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "standalone": false}, "@schematics/angular:directive": {"standalone": false}, "@schematics/angular:pipe": {"standalone": false}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/chattrix-front-end", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/assets", {"glob": "**/*", "input": "public"}], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.scss", "node_modules/bootstrap/dist/css/bootstrap.min.css"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "ChattrixFrontEnd:build:production"}, "development": {"buildTarget": "ChattrixFrontEnd:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/assets", {"glob": "**/*", "input": "public"}], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.scss", "node_modules/bootstrap/dist/css/bootstrap.min.css"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"]}}}}}}
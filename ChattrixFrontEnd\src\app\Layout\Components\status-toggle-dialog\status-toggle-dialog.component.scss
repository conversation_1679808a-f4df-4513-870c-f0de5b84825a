/* Status Toggle Dialog */
.status-toggle-dialog {
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Dialog Header */
.dialog-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.dialog-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;

  mat-icon {
    font-size: 24px;
    width: 24px;
    height: 24px;
  }
}

/* Dialog Content */
.dialog-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
  background: var(--bg-primary);
}

/* User Info Section */
.user-info-section {
  margin-bottom: var(--spacing-lg);
}

.user-summary {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  flex-shrink: 0;

  .avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .avatar-initials {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
  }

  &.has-image {
    background: transparent;
  }
}

.user-details {
  flex: 1;
}

.user-name {
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-xs) 0;
}

.user-email {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0 0 var(--spacing-xs) 0;
}

.user-roles {
  color: var(--text-muted);
  font-size: 0.75rem;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Confirmation Section */
.confirmation-section {
  margin-bottom: var(--spacing-lg);
}

.confirmation-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
}

.confirmation-content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
}

.confirmation-text {
  flex: 1;
}

.confirmation-text h4 {
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-sm) 0;
}

.status-warning {
  color: #d32f2f;
  font-size: 0.875rem;
  margin: 0;
}

.status-info {
  color: #2e7d32;
  font-size: 0.875rem;
  margin: 0;
}

/* Icon Styling */
.activate-icon {
  color: #2e7d32;
}

.deactivate-icon {
  color: #d32f2f;
}

/* Dialog Actions */
.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.cancel-button {
  color: var(--text-secondary);

  &:hover {
    background: var(--bg-hover);
  }
}

.activate-button {
  background: #000000 !important; /* Black background for minimalist design */
  color: white !important;
  border: 2px solid white !important;

  mat-icon {
    margin-right: var(--spacing-xs);
    font-size: 18px;
    width: 18px;
    height: 18px;
  }

  &:hover {
    background: #333333 !important;
  }
}

.deactivate-button {
  background: #000000 !important; /* Black background for minimalist design */
  color: white !important;
  border: 2px solid white !important;

  mat-icon {
    margin-right: var(--spacing-xs);
    font-size: 18px;
    width: 18px;
    height: 18px;
  }

  &:hover {
    background: #333333 !important;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .status-toggle-dialog {
    max-width: 95vw;
    max-height: 95vh;
  }

  .dialog-header,
  .dialog-content,
  .dialog-actions {
    padding: var(--spacing-md);
  }

  .user-summary {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .user-avatar {
    width: 50px;
    height: 50px;

    .avatar-initials {
      font-size: 1rem;
    }
  }

  .confirmation-content {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .dialog-actions {
    flex-direction: column-reverse;
    gap: var(--spacing-sm);
  }

  .cancel-button,
  .activate-button,
  .deactivate-button {
    width: 100%;
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .dialog-header,
  .dialog-actions {
    background: #f5f5f5;
    border-color: #e0e0e0;
  }

  .dialog-content {
    background: #ffffff;
  }

  .dialog-title {
    color: #333333;
  }

  .user-summary,
  .confirmation-card {
    background: #ffffff;
    border-color: #e0e0e0;
  }

  .user-name {
    color: #333333;
  }

  .user-email {
    color: #666666;
  }

  .user-roles {
    color: #999999;
  }

  .confirmation-text h4 {
    color: #333333;
  }
}

<!-- Profile Page Container -->
<div class="profile-container" *ngIf="userProfile$ | async as userProfile">
  <!-- Unified Profile Card with Head<PERSON> and Tabs -->
  <div class="unified-profile-card">
    <mat-card class="main-profile-card">
      <!-- Profile Header Section -->
      <div class="profile-header-section">
        <div class="profile-info-section">
          <!-- Integrated Photo Management in Header -->
          <div
            class="profile-avatar clickable"
            [class.has-image]="imagePreview || userProfile.profilePictureUrl"
            (click)="triggerFileInput($event)"
            (keydown.enter)="triggerFileInput($event)"
            (keydown.space)="triggerFileInput($event)"
            tabindex="0"
            role="button"
            [attr.aria-label]="
              imagePreview || userProfile.profilePictureUrl
                ? 'Change profile picture'
                : 'Upload profile picture'
            "
            [matTooltip]="
              imagePreview || userProfile.profilePictureUrl
                ? 'Click to change photo'
                : 'Click to upload photo'
            "
            matTooltipPosition="below"
          >
            <!-- Preview or current image -->
            <img
              *ngIf="imagePreview"
              [src]="imagePreview"
              alt="Profile Preview"
              class="avatar-image"
            />
            <img
              *ngIf="!imagePreview && userProfile.profilePictureUrl"
              [src]="userProfile.profilePictureUrl"
              [alt]="getUserDisplayName(userProfile)"
              class="avatar-image"
              #avatarImg
              (error)="avatarImg.style.display = 'none'"
            />
            <!-- Initials when no image -->
            <span
              *ngIf="!imagePreview && !userProfile.profilePictureUrl"
              class="avatar-initials"
            >
              {{ getUserInitials(userProfile) }}
            </span>
            <!-- Hover overlay -->
            <div
              *ngIf="imagePreview || userProfile.profilePictureUrl"
              class="avatar-overlay"
            >
              <mat-icon class="edit-icon">edit</mat-icon>
              <span class="edit-text">Change</span>
            </div>
            <div
              *ngIf="!imagePreview && !userProfile.profilePictureUrl"
              class="avatar-overlay"
            >
              <mat-icon class="upload-icon">add_a_photo</mat-icon>
              <span class="upload-text">Add Photo</span>
            </div>
            <!-- Remove button -->
            <!-- <button
              *ngIf="imagePreview || userProfile.profilePictureUrl"
              type="button"
              mat-icon-button
              class="remove-avatar-btn"
              (click)="removeImage($event)"
              aria-label="Remove image"
              matTooltip="Remove photo"
              matTooltipPosition="right"
            >
              <mat-icon>close</mat-icon>
            </button> -->
          </div>

          <!-- Hidden file input -->
          <input
            type="file"
            id="profileFileInput"
            accept="image/jpeg,image/jpg,image/png"
            (change)="onFileSelected($event)"
            style="display: none"
          />

          <div class="profile-details">
            <div class="profile-name-section">
              <h1 class="profile-name">
                {{ getUserDisplayName(userProfile) }}
              </h1>
              <span class="role-badge">{{ getRoleDisplay(userProfile) }}</span>
            </div>
            <p class="profile-email">{{ userProfile.email }}</p>
          </div>
        </div>
      </div>

      <!-- Tab Navigation Section -->
      <div class="tab-navigation-section">
        <mat-tab-group
          mat-stretch-tabs="false"
          [selectedIndex]="selectedTabIndex"
          (selectedTabChange)="onTabChange($event.index)"
          class="profile-tabs"
        >
          <!-- Profile Info Tab -->
          <mat-tab
            label="Personal Info"
            [style]="{ color: 'var(--text-secondary) !important' }"
          >
            <div class="tab-content">
              <!-- Merged header section -->
              <div class="form-header">
                <h2 class="form-title">Personal Information</h2>
                <p class="form-subtitle">Update your personal details</p>
              </div>

              <!-- Content section -->
                  <div class="row"></div>
              <form
                [formGroup]="profileForm"
                (ngSubmit)="onSubmitProfile()"
                class="profile-form"
              >
                <!-- Full Name Field -->
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>First Name</mat-label>
                  <input
                    matInput
                    formControlName="fullName"
                    placeholder="Enter your full name"
                    autocomplete="name"
                  />
                  <mat-error *ngIf="getFieldError('fullName')">
                    {{ getFieldError("fullName") }}
                  </mat-error>
                </mat-form-field>

                <!-- Email Field (Read-only) -->
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Email</mat-label>
                  <input
                    matInput
                    [value]="userProfile.email"
                    disabled
                    class="readonly-field custom-disabled-field"
                  />
                  <mat-icon matSuffix>info</mat-icon>
                  <mat-hint [style]="{ color: 'var(--text-muted)' }"
                    >Email cannot be changed from profile settings</mat-hint
                  >
                </mat-form-field>

                <!-- Phone Number Field -->
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Phone Number</mat-label>
                  <input
                    matInput
                    formControlName="phoneNumber"
                    placeholder="+****************"
                    autocomplete="tel"
                  />
                  <mat-icon matSuffix>phone</mat-icon>
                </mat-form-field>

                <!-- User Role Field (Read-only) -->
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>User type</mat-label>
                  <input
                    matInput
                    [value]="getRoleDisplay(userProfile)"
                    disabled
                    class="readonly-field custom-disabled-field"
                  />
                  <mat-icon matSuffix>info</mat-icon>
                  <mat-hint [style]="{ color: 'var(--text-muted)' }"
                    >User role is managed by administrators</mat-hint
                  >
                </mat-form-field>

                <!-- Description Field -->
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Status</mat-label>
                  <textarea
                    style="resize: none"
                    matInput
                    formControlName="description"
                    placeholder="Add a brief description about yourself"
                    rows="4"
                    maxlength="50"
                  ></textarea>
                  <mat-icon matSuffix class="description-icon"
                    >description</mat-icon
                  >
                  <!-- <mat-hint align="end">
                    {{ profileForm.get("description")?.value?.length || 0 }}/500
                  </mat-hint> -->
                </mat-form-field>

                <!-- Action Buttons -->
                <div class="form-actions">
                  <!-- <button
                    type="button"
                    mat-stroked-button
                    class="cancel-btn"
                    (click)="onCancelProfile()"
                    [disabled]="isSubmitting"
                  >
                    Cancel
                  </button> -->

                  <button
                    type="submit"
                    mat-raised-button
                    class="submit-button"
                    [disabled]="profileForm.invalid || isSubmitting"
                  >
                    <mat-spinner
                      *ngIf="isSubmitting"
                      diameter="20"
                      class="button-spinner"
                    ></mat-spinner>
                    <span [class.hidden]="isSubmitting">Update Profile</span>
                  </button>
                </div>
              </form>
            </div>
          </mat-tab>

          <!-- Change Password Tab -->
          <mat-tab label="Change Password">
            <div class="tab-content">
              <!-- Merged header section -->
              <div class="form-header">
                <h2 class="form-title">Change Password</h2>
                <p class="form-subtitle">Update your account password</p>
              </div>

              <!-- Content section -->
              <div class="password-redirect">
                <p>Click the button below to change your password securely.</p>
                <button
                  mat-raised-button
                  class="submit-button"
                  (click)="onTabChange(1)"
                >
                  Change Password
                </button>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </div>
    </mat-card>
  </div>
</div>

<!-- Loading State -->
<div *ngIf="!(userProfile$ | async)" class="loading-container">
  <mat-spinner diameter="50"></mat-spinner>
  <p>Loading profile...</p>
</div>

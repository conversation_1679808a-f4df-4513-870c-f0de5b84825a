import { Injectable } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { NotificationService } from '../../../Core/Services/notification.service';

export interface AuthErrorMapping {
  statusCode: number;
  message: string;
  userFriendlyMessage: string;
}

@Injectable({
  providedIn: 'root',
})
export class AuthErrorHandlerService {
  constructor(private notificationService: NotificationService) {}

  handleAuthError(error: HttpErrorResponse): void {
    const userMessage = this.mapErrorToUserMessage(error);
    this.notificationService.showError(userMessage);
  }

  private mapErrorToUserMessage(error: HttpErrorResponse): string {
    // Handle different status codes
    switch (error.status) {
      case 400:
        return this.handle400Error(error);
      case 401:
        return this.handle401Error(error);
      case 403:
        return this.handle403Error(error);
      case 404:
        return this.handle404Error(error);
      case 500:
        return this.handle500Error(error);
      case 0:
        return this.handleNetworkError(error);
      default:
        return this.handleUnknownError(error);
    }
  }

  private handle400Error(error: HttpErrorResponse): string {
    const message = this.extractErrorMessage(error);
    const lowerMsg = message.toLowerCase();

    // Authentication validation errors
    if (
      lowerMsg.includes('invalid password') ||
      lowerMsg.includes('wrong password') ||
      lowerMsg.includes('incorrect password')
    ) {
      return 'Invalid password. Please try again.';
    }

    if (
      lowerMsg.includes('user not found') ||
      lowerMsg.includes('email not found')
    ) {
      return 'No account found with this email address. Please check your email or sign up.';
    }

    if (
      lowerMsg.includes('email already exists') ||
      lowerMsg.includes('user with this email already exists')
    ) {
      return 'An account with this email address already exists. Please use a different email or try logging in.';
    }

    // Account status errors
    if (lowerMsg.includes('deleted') || lowerMsg.includes('inactive')) {
      return 'Your account is inactive. Please contact support to reactivate your account.';
    }

    if (lowerMsg.includes('locked')) {
      return 'Your account has been locked. Please try again later or contact support.';
    }

    // OTP errors
    if (lowerMsg.includes('otp') || lowerMsg.includes('verification code')) {
      if (lowerMsg.includes('expired')) {
        return 'Verification code has expired. Please request a new one.';
      }
      if (lowerMsg.includes('invalid') || lowerMsg.includes('incorrect')) {
        return 'Invalid verification code. Please try again.';
      }
      return 'Verification code error. Please try again.';
    }

    // Password reset errors
    if (
      lowerMsg.includes('reset') &&
      (lowerMsg.includes('token') || lowerMsg.includes('link'))
    ) {
      if (lowerMsg.includes('expired')) {
        return 'Reset link has expired. Please request a new password reset.';
      }
      if (lowerMsg.includes('invalid')) {
        return 'Invalid reset link. Please request a new password reset.';
      }
    }

    // Generic validation error
    return message || 'Please check your input and try again.';
  }

  private handle401Error(error: HttpErrorResponse): string {
    const message = this.extractErrorMessage(error);
    const lowerMsg = message.toLowerCase();

    if (
      lowerMsg.includes('token expired') ||
      lowerMsg.includes('session expired')
    ) {
      return 'Your session has expired. Please log in again.';
    }

    return 'Authentication failed. Please check your credentials and try again.';
  }

  private handle403Error(error: HttpErrorResponse): string {
    return 'Access denied. You do not have permission to perform this action.';
  }

  private handle404Error(error: HttpErrorResponse): string {
    const message = this.extractErrorMessage(error);
    const lowerMsg = message.toLowerCase();

    if (lowerMsg.includes('user') || lowerMsg.includes('account')) {
      return 'Account not found. Please check your email address.';
    }

    return 'The requested resource was not found.';
  }

  private handle500Error(error: HttpErrorResponse): string {
    return 'Server error occurred. Please try again later or contact support if the problem persists.';
  }

  private handleNetworkError(error: HttpErrorResponse): string {
    return 'Network connection error. Please check your internet connection and try again.';
  }

  private handleUnknownError(error: HttpErrorResponse): string {
    const message = this.extractErrorMessage(error);
    return message || 'An unexpected error occurred. Please try again.';
  }

  private extractErrorMessage(error: HttpErrorResponse): string {
    // Try different possible error message locations
    if (error.error?.message) {
      return error.error.message;
    }

    if (error.error?.error_description) {
      return error.error.error_description;
    }

    if (error.error?.Error) {
      return error.error.Error;
    }

    if (typeof error.error === 'string') {
      return error.error;
    }

    if (error.message) {
      return error.message;
    }

    return 'An error occurred';
  }

  showSuccess(message: string): void {
    this.notificationService.showSuccess(message);
  }

  showInfo(message: string): void {
    this.notificationService.showInfo(message);
  }
}

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { finalize, tap } from 'rxjs/operators';
import { environment } from '../../../../Environments/environment';
import {
  LoginRequest,
  RegisterRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  ChangePasswordRequest,
  UserInfo,
  AuthResponse,
  RegisterResponse,
  LoginResponse,
  ForgotPasswordResponse,
  ResetPasswordResponse,
  ApiResponse,
} from '../Models';
import { AuthStateService } from './AuthState.service';

@Injectable({ providedIn: 'root' })
export class AuthService {
  private readonly API_URL = `${environment.apiUrl}/api/Account`;

  constructor(
    private readonly http: HttpClient,
    private readonly state: AuthStateService,
    private readonly router: Router,
  ) {}

  login(data: LoginRequest): Observable<LoginResponse> {
    this.state.setLoading(true);
    this.state.clearError();
    return this.http.post<LoginResponse>(`${this.API_URL}/Login`, data).pipe(
      tap((res) => {
        if (res.isSuccess && res.token) {
          const user = this.decodeUserInfo(res.token);
          if (user) {
            this.saveSession(res.token);
            this.state.setLoginSuccess(user, res.token);
          } else {
            this.state.setLoginFailure('Invalid user info in token');
          }
        } else {
          this.state.setLoginFailure(res.message || 'Login failed');
        }
      }),
      finalize(() => this.state.setLoading(false)),
    );
  }

  //   register(data: RegisterRequest): Observable<RegisterResponse> {
  //     this.state.setLoading(true);
  //     this.state.clearError();
  //     return this.http
  //       .post<RegisterResponse>(`${this.API_URL}/Register`, data)
  //       .pipe(
  //         tap((res) => {
  //           if (res.isSuccess) {
  //             this.showSuccess(
  //               'Registration successful! Please check your email.',
  //             );
  //           } else {
  //             this.showError(res.message || 'Registration failed');
  //           }
  //         }),
  //         catchError((err) => this.handleHttpError(err)),
  //         finalize(() => this.state.setLoading(false)),
  //       );
  //   }

  forgotPassword(
    data: ForgotPasswordRequest,
  ): Observable<ForgotPasswordResponse> {
    this.state.setLoading(true);
    this.state.clearError();
    return this.http
      .post<ForgotPasswordResponse>(`${this.API_URL}/ForgotPassword`, data)
      .pipe(finalize(() => this.state.setLoading(false)));
  }

  resetPassword(data: ResetPasswordRequest): Observable<ResetPasswordResponse> {
    this.state.setLoading(true);
    this.state.clearError();
    return this.http
      .post<ResetPasswordResponse>(`${this.API_URL}/ResetPassword`, data)
      .pipe(finalize(() => this.state.setLoading(false)));
  }

  changePassword(data: ChangePasswordRequest): Observable<ApiResponse> {
    this.state.setLoading(true);
    this.state.clearError();
    return this.http
      .post<ApiResponse>(`${this.API_URL}/ChangePassword`, data)
      .pipe(finalize(() => this.state.setLoading(false)));
  }

  logout(): void {
    this.clearSession();
    this.state.setLogoutState();
    this.router.navigate(['/auth/login']);
  }

  private saveSession(token: string): void {
    localStorage.setItem('chattrix_auth_token', token);
    const user = this.decodeUserInfo(token);
    if (user) {
      localStorage.setItem('chattrix_user_info', JSON.stringify(user));
    }
  }

  clearSession(): void {
    localStorage.removeItem('chattrix_auth_token');
    localStorage.removeItem('chattrix_user_info');
  }

  private decodeUserInfo(token: string): UserInfo | null {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return {
        id: payload.id || payload.nameid || '',
        name: payload.FullName || payload.name || '',
        email: payload.Email || payload.email || '',
        role: payload.role || [],
        isActive: payload.IsActive ?? true,
        phoneNumber: payload.PhoneNumber,
        description: payload.Description,
        profilePictureUrl: payload.ProfilePictureUrl,
      };
    } catch {
      return null;
    }
  }

  loginWith2FA(userId: string, otp: string): Observable<AuthResponse> {
    this.state.setLoading(true);
    this.state.clearError();
    return this.http
      .post<AuthResponse>(`${this.API_URL}/VerifyOtp`, { userId, otp })
      .pipe(
        tap((res) => {
          if (res.isSuccess && res.token) {
            const user = this.decodeUserInfo(res.token);
            if (user) {
              this.saveSession(res.token);
              this.state.setLoginSuccess(user, res.token);
            } else {
              this.state.setLoginFailure('Invalid user info in token');
            }
          } else {
            this.state.setLoginFailure(res.message || '2FA failed');
          }
        }),
        finalize(() => this.state.setLoading(false)),
      );
  }

  resendOtp(userId: string): Observable<AuthResponse> {
    this.state.setLoading(true);
    this.state.clearError();
    return this.http
      .post<AuthResponse>(`${this.API_URL}/ResendOtp`, { userId })
      .pipe(finalize(() => this.state.setLoading(false)));
  }

  verifyResetToken(data: {
    email: string;
    resetToken: string;
  }): Observable<AuthResponse> {
    this.state.setLoading(true);
    this.state.clearError();
    return this.http
      .post<AuthResponse>(`${this.API_URL}/VerifyResetToken`, data)
      .pipe(finalize(() => this.state.setLoading(false)));
  }

  getToken(): string | null {
    return localStorage.getItem('chattrix_auth_token');
  }

  isTokenExpired(): boolean {
    const token = this.getToken();
    if (!token) return true;
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  }

  getUserInfo(): UserInfo | null {
    const user = localStorage.getItem('chattrix_user_info');
    return user ? JSON.parse(user) : null;
  }

  initializeAuthState(): void {
    const token = this.getToken();
    const user = this.getUserInfo();
    if (token && !this.isTokenExpired() && user) {
      this.state.setLoginSuccess(user, token);
    } else {
      this.clearSession();
      this.state.reset();
    }
  }
}

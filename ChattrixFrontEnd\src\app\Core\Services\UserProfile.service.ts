import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, map } from 'rxjs';
import { AuthStateService } from '../../Pages/authentication/Services/AuthState.service';
import { UserInfo } from '../../Pages/authentication/Models';

export interface UserProfile extends UserInfo {
  profilePictureUrl?: string;
  displayName: string;
  initials: string;
}

@Injectable({
  providedIn: 'root',
})
export class UserProfileService {
  private readonly S3_BASE_URL =
    'https://fullstacksfl.s3.eu-north-1.amazonaws.com/';

  private _userProfile$ = new BehaviorSubject<UserProfile | null>(null);
  public readonly userProfile$ = this._userProfile$.asObservable();

  constructor(private authState: AuthStateService) {
    // Subscribe to auth state changes and update user profile
    this.authState.user$.subscribe((user) => {
      if (user) {
        this.updateUserProfile(user);
      } else {
        this._userProfile$.next(null);
      }
    });
  }

  /**
   * Updates the user profile based on UserInfo
   */
  private updateUserProfile(user: UserInfo): void {
    const profile: UserProfile = {
      ...user,
      displayName: this.getDisplayName(user),
      initials: this.getInitials(user),
      profilePictureUrl: this.getProfilePictureUrl(user),
    };
    this._userProfile$.next(profile);
  }

  /**
   * Gets the current user profile
   */
  get currentProfile(): UserProfile | null {
    return this._userProfile$.value;
  }

  /**
   * Gets the display name for the user
   */
  private getDisplayName(user: UserInfo): string {
    return user.name || user.email || 'User';
  }

  /**
   * Gets the initials for the user
   */
  private getInitials(user: UserInfo): string {
    const name = user.name || user.email || 'user';
    const words = name.split(' ').filter((word) => word.length > 0);

    if (words.length >= 2) {
      return (words[0][0] + words[1][0]).toUpperCase();
    } else if (words.length === 1) {
      return words[0].substring(0, 2).toUpperCase();
    }

    return 'user';
  }

  /**
   * Constructs the profile picture URL from S3
   */
  private getProfilePictureUrl(user: UserInfo): string | undefined {
    if (!user.profilePictureUrl) {
      return undefined;
    }

    // If it's already a full URL, return as is
    if (user.profilePictureUrl.startsWith('http')) {
      return user.profilePictureUrl;
    }

    // Otherwise, construct the full S3 URL
    return this.getS3Url(user.profilePictureUrl);
  }

  /**
   * Checks if the current user has admin access
   * Simplified approach: anyone who is NOT just a "user" has admin access
   */
  hasAdminAccess(): Observable<boolean> {
    return this.userProfile$.pipe(
      map((profile) => {
        if (!profile) return false;

        const roles = Array.isArray(profile.role)
          ? profile.role
          : [profile.role];

        // Check if user has only "user" role (case-insensitive)
        const hasOnlyUserRole =
          roles.length === 1 && roles[0].toLowerCase() === 'user';

        // If user has only "user" role, they don't have admin access
        if (hasOnlyUserRole) {
          return false;
        }

        // Check for admin or super admin roles (case-insensitive)
        const hasAdminRole = roles.some(
          (role) =>
            role.toLowerCase() === 'admin' ||
            role.toLowerCase() === 'super admin' ||
            role.toLowerCase() === 'superadmin',
        );

        return hasAdminRole;
      }),
    );
  }

  /**
   * Checks if the current user has a specific role
   */
  hasRole(targetRole: string): Observable<boolean> {
    return this.userProfile$.pipe(
      map((profile) => {
        if (!profile) return false;

        const roles = Array.isArray(profile.role)
          ? profile.role
          : [profile.role];
        return roles.some(
          (role) => role.toLowerCase() === targetRole.toLowerCase(),
        );
      }),
    );
  }

  /**
   * Gets all roles for the current user
   */
  getUserRoles(): Observable<string[]> {
    return this.userProfile$.pipe(
      map((profile) => {
        if (!profile) return [];
        return Array.isArray(profile.role) ? profile.role : [profile.role];
      }),
    );
  }

  /**
   * Gets the role display string
   */
  getRoleDisplay(): Observable<string> {
    return this.userProfile$.pipe(
      map((profile) => {
        if (!profile) return 'User';

        if (Array.isArray(profile.role)) {
          return profile.role.join(', ');
        }
        return profile.role || 'User';
      }),
    );
  }

  /**
   * Constructs full S3 URL from a key
   */
  getS3Url(key: string): string {
    if (!key) return '';

    // If it's already a full URL, return as is
    if (key.startsWith('http')) {
      return key;
    }

    // Otherwise, construct the full S3 URL
    return `${this.S3_BASE_URL}${key}`;
  }

  /**
   * Gets a fallback avatar URL or returns undefined for initials display
   */
  getAvatarUrl(): Observable<string | undefined> {
    return this.userProfile$.pipe(map((profile) => profile?.profilePictureUrl));
  }
}

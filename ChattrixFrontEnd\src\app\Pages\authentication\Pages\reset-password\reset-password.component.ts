import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  AbstractControl,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import { AuthService } from '../../Services/auth.service';
import { AuthStateService } from '../../Services/AuthState.service';
import { NotificationService } from '../../../../Core/Services/notification.service';
import { ResetPasswordRequest, AuthState } from '../../Models';

@Component({
  selector: 'app-reset-password',
  standalone: false,
  templateUrl: './reset-password.component.html',
  styleUrl: './reset-password.component.scss',
})
export class ResetPasswordComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  resetPasswordForm: FormGroup;
  hidePassword = true;
  hideConfirmPassword = true;
  logoLoaded = true; // Assume logo loads successfully by default

  // Route parameters
  email: string = '';
  resetToken: string = '';

  // Authentication state
  isLoading = false;
  error: string | null = null;

  constructor(
    private authService: AuthService,
    private authState: AuthStateService,
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private notificationService: NotificationService,
  ) {
    this.resetPasswordForm = this.formBuilder.group(
      {
        newPassword: [
          '',
          [
            Validators.required,
            Validators.minLength(8),
            this.passwordValidator,
          ],
        ],
        confirmPassword: ['', [Validators.required]],
      },
      { validators: this.passwordMatchValidator },
    );
  }

  ngOnInit(): void {
    // Get email and token from query parameters
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe((params) => {
        this.email = params['email'];
        this.resetToken = params['token'];

        if (!this.email || !this.resetToken) {
          this.router.navigate(['/auth/forgot-password']);
          return;
        }
      });

    // Subscribe to authentication state
    this.authState.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: AuthState) => {
        this.isLoading = state.isLoading;
        this.error = state.error;

        // Error handling is now done centrally through interceptors
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onResetPassword(): void {
    // Clear any previous errors
    this.authState.clearError();

    // Validate form first
    if (!this.resetPasswordForm.valid) {
      this.handleFormValidation();
      return;
    }

    const resetPasswordData: ResetPasswordRequest = {
      email: this.email,
      resetToken: this.resetToken,
      newPassword: this.resetPasswordForm.value.newPassword,
      confirmPassword: this.resetPasswordForm.value.confirmPassword,
    };

    this.authService.resetPassword(resetPasswordData).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          this.notificationService.showSuccess(
            'Password reset successfully! You can now login with your new password.',
          );
          // Navigate to login page
          this.router.navigate(['/auth/login']);
        }
      },
      error: (error: any) => {
        console.error('Password reset failed:', error);
      },
    });
  }

  onBackToLogin(): void {
    this.router.navigate(['/auth/login']);
  }

  togglePasswordVisibility(): void {
    this.hidePassword = !this.hidePassword;
  }

  toggleConfirmPasswordVisibility(): void {
    this.hideConfirmPassword = !this.hideConfirmPassword;
  }

  onImageError(event: any): void {
    this.logoLoaded = false;
    console.log('Logo failed to load, showing fallback');
  }

  // Custom Validators
  private passwordValidator(
    control: AbstractControl,
  ): { [key: string]: any } | null {
    const value = control.value;
    if (!value) return null;

    const hasUpperCase = /[A-Z]/.test(value);
    const hasLowerCase = /[a-z]/.test(value);
    const hasNumeric = /[0-9]/.test(value);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);

    const valid = hasUpperCase && hasLowerCase && hasNumeric && hasSpecialChar;

    if (!valid) {
      return {
        passwordStrength: {
          hasUpperCase,
          hasLowerCase,
          hasNumeric,
          hasSpecialChar,
        },
      };
    }

    return null;
  }

  private passwordMatchValidator(
    group: AbstractControl,
  ): { [key: string]: any } | null {
    const password = group.get('newPassword');
    const confirmPassword = group.get('confirmPassword');

    if (!password || !confirmPassword) return null;

    return password.value === confirmPassword.value
      ? null
      : { passwordMismatch: true };
  }

  private markFormGroupTouched(): void {
    Object.keys(this.resetPasswordForm.controls).forEach((key) => {
      const control = this.resetPasswordForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Handles form validation errors
   */
  private handleFormValidation(): void {
    this.markFormGroupTouched();

    const newPasswordControl = this.resetPasswordForm.get('newPassword');
    const confirmPasswordControl =
      this.resetPasswordForm.get('confirmPassword');

    if (newPasswordControl?.hasError('required')) {
      this.notificationService.showError('New password is required.');
      return;
    }

    if (newPasswordControl?.hasError('minlength')) {
      this.notificationService.showError(
        'Password must be at least 8 characters long.',
      );
      return;
    }

    if (newPasswordControl?.hasError('pattern')) {
      this.notificationService.showError(
        'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character.',
      );
      return;
    }

    if (confirmPasswordControl?.hasError('required')) {
      this.notificationService.showError('Please confirm your password.');
      return;
    }

    if (this.resetPasswordForm.hasError('passwordMismatch')) {
      this.notificationService.showError(
        'Passwords do not match. Please try again.',
      );
      return;
    }

    this.notificationService.showError(
      'Please check your input and try again.',
    );
  }

  // Getter methods for template
  get newPasswordControl() {
    return this.resetPasswordForm.get('newPassword');
  }
  get confirmPasswordControl() {
    return this.resetPasswordForm.get('confirmPassword');
  }
}

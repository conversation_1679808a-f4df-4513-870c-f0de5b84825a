import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { UserListComponent } from './Pages/user-list/user-list.component';
import { AddEditUserComponent } from './Pages/add-edit-user/add-edit-user.component';
import { UserDetailsComponent } from './Pages/user-details/user-details.component';
import { AdminGuard } from '../../Core/Guards/admin.guard';
import { AuthGuard } from '../../Core/Guards/auth.guard';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'list',
    pathMatch: 'full',
  },
  {
    path: 'list',
    component: UserListComponent,
    title: 'User Management - Chattrix',
    canActivate: [AuthGuard, AdminGuard],
  },
  {
    path: 'add',
    component: AddEditUserComponent,
    title: 'Add User - Chattrix',
    canActivate: [AuthGuard, AdminGuard],
  },
  {
    path: 'edit/:id',
    component: AddEditUserComponent,
    data: {},
    title: 'Edit User - Chattrix',
    canActivate: [AuthGuard, AdminGuard],
  },
  {
    path: 'details/:id',
    component: UserDetailsComponent,
    title: 'User Details - Chattrix',
    canActivate: [AuthGuard],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UserManagementRoutingModule {}

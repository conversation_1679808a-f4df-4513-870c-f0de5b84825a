import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil, interval, Subscription } from 'rxjs';

import { AuthService } from '../../Services/auth.service';
import { AuthStateService } from '../../Services/AuthState.service';
import { NotificationService } from '../../../../Core/Services/notification.service';
import { AuthState, UserInfo } from '../../Models';

@Component({
  selector: 'app-otp-verification',
  standalone: false,
  templateUrl: './otp-verification.component.html',
  styleUrl: './otp-verification.component.scss',
})
export class OtpVerificationComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private timerSubscription?: Subscription;

  otpForm: FormGroup;
  userId: string = '';
  logoLoaded = true; // Assume logo loads successfully by default

  // Timer state
  resendTimer = 0;
  canResend = false;

  // Authentication state
  isAuthenticated = false;
  isLoading = false;
  isResending = false;
  user: UserInfo | null = null;
  error: string | null = null;

  constructor(
    private authService: AuthService,
    private authState: AuthStateService,
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private notificationService: NotificationService,
  ) {
    this.otpForm = this.formBuilder.group({
      otpCode: [
        '',
        [
          Validators.required,
          Validators.pattern(/^\d{5}$/),
          Validators.minLength(5),
          Validators.maxLength(5),
        ],
      ],
    });
  }

  ngOnInit(): void {
    // Get userId from query parameters
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe((params) => {
        this.userId = params['userId'];
        if (!this.userId) {
          this.router.navigate(['/auth/login']);
          return;
        }
      });

    // Subscribe to authentication state
    this.authState.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: AuthState) => {
        this.isAuthenticated = state.isAuthenticated;
        this.isLoading = state.isLoading;
        this.user = state.user;
        this.error = state.error;

        // Error handling is now done centrally through interceptors

        // Redirect if authenticated
        if (state.isAuthenticated && state.user) {
          this.router.navigate(['/dashboard']);
        }
      });

    // Start resend timer
    this.startResendTimer();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.timerSubscription?.unsubscribe();
  }

  onOtpInput(event: any): void {
    const value = event.target.value;

    // Only allow numeric characters
    const numericValue = value.replace(/\D/g, '');

    // Limit to 5 digits
    const limitedValue = numericValue.slice(0, 5);

    // Update the form control
    this.otpForm.get('otpCode')?.setValue(limitedValue);

    // Auto-submit if 5 digits are entered
    if (limitedValue.length === 5) {
      setTimeout(() => this.onVerifyOtp(), 100);
    }
  }

  onOtpPaste(event: ClipboardEvent): void {
    event.preventDefault();
    const pastedText = event.clipboardData?.getData('text') || '';

    // Extract only numeric characters and limit to 5 digits
    const numericValue = pastedText.replace(/\D/g, '').slice(0, 5);

    // Update the form control
    this.otpForm.get('otpCode')?.setValue(numericValue);

    // Auto-submit if 5 digits are pasted
    if (numericValue.length === 5) {
      setTimeout(() => this.onVerifyOtp(), 100);
    }
  }

  onVerifyOtp(): void {
    // Clear any previous errors
    this.authState.clearError();

    const otp = this.otpForm.get('otpCode')?.value || '';

    // Validate OTP format first
    if (!this.otpForm.valid || otp.length !== 5 || !/^\d{5}$/.test(otp)) {
      this.handleFormValidation();
      return;
    }

    this.authService.loginWith2FA(this.userId, otp).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          this.notificationService.showSuccess(
            'OTP verified successfully! Welcome back.',
          );
          // Navigation will be handled by auth state subscription
          this.router.navigate(['/dashboard']);
        }
      },
      error: (error: any) => {
        console.error('OTP verification failed:', error);
        this.clearOtp();
      },
    });
  }

  onResendOtp(): void {
    if (!this.canResend) return;

    this.isResending = true;

    this.authService.resendOtp(this.userId).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          this.notificationService.showSuccess(
            'New verification code sent successfully! Please check your device.',
          );
          this.clearOtp();
          this.startResendTimer();
        }
        this.isResending = false;
      },
      error: (error: any) => {
        console.error('Resend OTP failed:', error);
        this.isResending = false;
      },
    });
  }

  onBackToLogin(): void {
    this.router.navigate(['/auth/login']);
  }

  onImageError(_event: any): void {
    this.logoLoaded = false;
    console.log('Logo failed to load, showing fallback');
  }

  private clearOtp(): void {
    this.otpForm.get('otpCode')?.setValue('');
    this.otpForm.get('otpCode')?.markAsUntouched();
  }

  private startResendTimer(): void {
    this.resendTimer = 60; // 60 seconds
    this.canResend = false;

    this.timerSubscription?.unsubscribe();
    this.timerSubscription = interval(1000)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.resendTimer--;
        if (this.resendTimer <= 0) {
          this.canResend = true;
          this.timerSubscription?.unsubscribe();
        }
      });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.otpForm.controls).forEach((key) => {
      const control = this.otpForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Handles form validation errors
   */
  private handleFormValidation(): void {
    this.markFormGroupTouched();

    const otpControl = this.otpForm.get('otpCode');

    if (otpControl?.hasError('required')) {
      this.notificationService.showError('Verification code is required.');
      return;
    }

    if (
      otpControl?.hasError('pattern') ||
      otpControl?.hasError('minlength') ||
      otpControl?.hasError('maxlength')
    ) {
      this.notificationService.showError(
        'Please enter a valid 5-digit verification code.',
      );
      return;
    }

    this.notificationService.showError(
      'Please enter a valid 5-digit verification code.',
    );
  }

  // Getter for timer display
  get timerDisplay(): string {
    const minutes = Math.floor(this.resendTimer / 60);
    const seconds = this.resendTimer % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}

<div class="user-management-container">
  <!-- Table Section with Integrated Header and Filters -->
  <div class="table-section">
    <mat-card class="table-card">
      <!-- Reusable Card Header Component -->
      <app-card-header
        title="User List"
        subtitle="Manage your team members and their account permissions here."
      >
        <!-- Action buttons projected into header -->
        <button
          mat-raised-button
          color="primary"
          class="edit-btn"
          (click)="onAddUser()"
          *ngIf="hasAdminAccess"
        >
          <mat-icon>person_add</mat-icon>
          Add User
        </button>
      </app-card-header>

      <!-- Table Header with Filters -->
      <div class="table-header">
        <div class="table-header-left">
          <div class="filters-container">
            <!-- Search Input -->
            <mat-form-field
              class="filter-field search-field"
              appearance="outline"
            >
              <mat-label>Search</mat-label>
              <input
                matInput
                [formControl]="searchControl"
                placeholder="Search users..."
              />
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>

            <!-- Role Filter -->
            <mat-form-field
              class="filter-field role-field"
              appearance="outline"
            >
              <mat-label>Role</mat-label>
              <mat-select [formControl]="roleFilter">
                <mat-option value="">All</mat-option>
                <mat-option value="admin">Admin</mat-option>
                <mat-option value="user">User</mat-option>
                <mat-option value="super admin">Super Admin</mat-option>
              </mat-select>
            </mat-form-field>

            <!-- Status Filter -->
            <mat-form-field
              class="filter-field status-field"
              appearance="outline"
            >
              <mat-label>Status</mat-label>
              <mat-select [formControl]="statusFilter">
                <mat-option value="all">All</mat-option>
                <mat-option value="active">Active</mat-option>
                <mat-option value="inactive">Inactive</mat-option>
              </mat-select>
            </mat-form-field>

            <!-- Reset Button -->
            <!-- <button
              mat-stroked-button
              class="reset-btn"
              (click)="resetFilters()"
              title="Reset filters"
            >
              <mat-icon>refresh</mat-icon>
              Reset
            </button> -->
          </div>
        </div>

        <!-- Add User button moved to card header -->
      </div>

      <!-- Loading Indicator -->
      <div *ngIf="loadingStates.fetchingUsers" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p class="loading-text">Loading users...</p>
      </div>

      <!-- Table Content -->
      <div *ngIf="!loadingStates.fetchingUsers" class="table-container">
        <table
          mat-table
          [dataSource]="dataSource"
          matSort
          (matSortChange)="onSortChange($event)"
          class="users-table"
        >
          <!-- Full Name Column -->
          <ng-container matColumnDef="fullName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Full Name</th>
            <td mat-cell *matCellDef="let user">
              <div class="user-info">
                <!-- Profile Picture with Fallback -->
                <div
                  class="user-avatar"
                  [class.has-image]="getProfilePictureUrl(user)"
                >
                  <img
                    *ngIf="getProfilePictureUrl(user)"
                    [src]="getProfilePictureUrl(user)"
                    [alt]="user.fullName || 'User'"
                    class="avatar-image"
                    #avatarImg
                    (error)="avatarImg.style.display = 'none'"
                  />
                  <span
                    *ngIf="!getProfilePictureUrl(user)"
                    class="avatar-initials"
                  >
                    {{ getUserInitials(user) }}
                  </span>
                </div>
                <div class="user-details">
                  <span class="user-name">{{ user.fullName || "N/A" }}</span>
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Email Column -->
          <ng-container matColumnDef="email">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
            <td mat-cell *matCellDef="let user">
              <span class="user-email">{{ user.email || "N/A" }}</span>
            </td>
          </ng-container>

          <!-- Roles Column -->
          <ng-container matColumnDef="roles">
            <th mat-header-cell *matHeaderCellDef>Roles</th>
            <td mat-cell *matCellDef="let user">
              <div class="roles-container">
                <mat-chip-set>
                  <!-- <mat-chip
                    *ngFor="let role of user.roles"
                    [class]="
                      'role-chip role-' + role.toLowerCase().replace(' ', '-')
                    "
                  > -->
                  {{ user.roles }}
                  <!-- </mat-chip> -->
                </mat-chip-set>
              </div>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="isActive">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
            <td mat-cell *matCellDef="let user">
              <mat-select
                [value]="user.isActive"
                (selectionChange)="onStatusChange(user, $event.value)"
                class="status-dropdown"
                [class.status-active]="user.isActive"
                [class.status-inactive]="!user.isActive"
                panelClass="status-dropdown-panel"
              >
                <mat-option [value]="true" class="status-option-active">
                  <!-- <mat-icon>check_circle</mat-icon> -->
                  Active
                </mat-option>
                <mat-option [value]="false" class="status-option-inactive">
                  <!-- <mat-icon>cancel</mat-icon> -->
                  Inactive
                </mat-option>
              </mat-select>
            </td>
          </ng-container>

          <!-- Created On Column -->
          <ng-container matColumnDef="createdOn">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Created On
            </th>
            <td mat-cell *matCellDef="let user">
              <span class="created-date">{{
                user.formattedCreatedOn | date: "dd MMM yyyy"
              }}</span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let user">
              <div class="actions-container">
                <button
                  mat-icon-button
                  *ngIf="user.actions.canView"
                  (click)="onViewUser(user)"
                  matTooltip="View Details"
                  class="action-btn view-btn"
                >
                  <mat-icon>visibility</mat-icon>
                </button>
                <button
                  mat-icon-button
                  *ngIf="user.actions.canEdit"
                  (click)="onEditUser(user)"
                  matTooltip="Edit User"
                  class="action-btn edit-btn"
                >
                  <mat-icon>edit</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>

        <!-- Empty State -->
        <div *ngIf="dataSource.data.length === 0" class="empty-state">
          <mat-icon class="empty-icon">people_outline</mat-icon>
          <h3>No users found</h3>
          <p>No users match your current filters.</p>
        </div>
      </div>

      <!-- Pagination -->
      <mat-paginator
        *ngIf="!loadingStates.fetchingUsers && dataSource.data.length > 0"
        [length]="totalItems"
        [pageSize]="pageSize"
        [pageSizeOptions]="pageSizeOptions"
        [pageIndex]="pageIndex"
        (page)="onPageChange($event)"
        showFirstLastButtons
        class="mat-mdc-paginator"
      ></mat-paginator>
    </mat-card>
  </div>
</div>

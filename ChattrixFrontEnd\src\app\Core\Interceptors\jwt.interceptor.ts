import { Injectable } from '@angular/core';
import {
  HttpRe<PERSON>,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { filter, take, switchMap } from 'rxjs/operators';
import { Router } from '@angular/router';

import { AuthService } from '../../Pages/authentication/Services/auth.service';
import { AuthStateService } from '../../Pages/authentication/Services/AuthState.service';

import { API_URL_PATTERNS } from '../Utils/Constants';

@Injectable()
export class JwtInterceptor implements HttpInterceptor {
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(
    null,
  );

  constructor(
    private authService: AuthService,
    private router: Router,
  ) {}

  intercept(
    request: HttpRequest<unknown>,
    next: <PERSON>ttp<PERSON><PERSON><PERSON>,
  ): Observable<HttpEvent<unknown>> {
    const token = this.authService.getToken();
    const isApiUrl = this.isApiRequest(request.url);

    if (token && isApiUrl) {
      request = this.addTokenHeader(request, token);
    }

    return next.handle(request);
  }

  private addTokenHeader(
    request: HttpRequest<any>,
    token: string,
  ): HttpRequest<any> {
    return request.clone({
      headers: request.headers.set('Authorization', `Bearer ${token}`),
    });
  }

  private isApiRequest(url: string): boolean {
    return API_URL_PATTERNS.some(
      (pattern) => url.startsWith(pattern) || url.includes(pattern),
    );
  }
}

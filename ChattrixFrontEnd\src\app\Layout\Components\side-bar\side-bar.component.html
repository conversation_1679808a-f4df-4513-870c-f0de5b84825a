<div
  class="sidebar-container"
  [class.collapsed]="isCollapsed"
  *ngIf="userProfile$ | async as userProfile"
>
  <!-- Header Section with Toggle Button -->
  <div class="header-section">
    <!-- Toggle Button (replaces logo when expanded) -->

    <!-- Logo and App Name (hidden when collapsed) -->
    <div *ngIf="!isCollapsed" class="app-branding">
      <div class="app-logo">
        <img
          src="logo/logo2.png"
          alt="Chattrix Logo"
          class="logo-image"
          #logoImg
          (error)="logoImg.style.display = 'none'"
        />
        <span
          class="logo-fallback"
          [style.display]="logoImg.style.display === 'none' ? 'flex' : 'none'"
          >C</span
        >
      </div>
      <h2 class="app-name">Chattrix</h2>
    </div>
    <button
      *ngIf="!isCollapsed"
      class="sidebar-toggle"
      (click)="toggleSidebar()"
      mat-icon-button
      matTooltip="Collapse sidebar"
      matTooltipPosition="right"
      aria-label="Collapse sidebar"
    >
      <mat-icon>chevron_left</mat-icon>
    </button>

    <!-- Expand <PERSON><PERSON> (visible when collapsed) -->
    <button
      *ngIf="isCollapsed"
      class="expand-toggle"
      (click)="toggleSidebar()"
      mat-icon-button
      matTooltip="Expand sidebar"
      matTooltipPosition="right"
      aria-label="Expand sidebar"
    >
      <mat-icon>chevron_right</mat-icon>
    </button>
  </div>

  <!-- Navigation Menu -->
  <div class="navigation-section">
    <mat-nav-list class="nav-list">
      <ng-container *ngFor="let item of navigationItems">
        <mat-list-item
          *ngIf="shouldShowNavItem(item, (hasAdminAccess$ | async) || false)"
          class="nav-item"
          [class.active]="isRouteActive(item.route)"
          (click)="onNavItemClick(item)"
          [matTooltip]="isCollapsed ? item.label : ''"
          matTooltipPosition="right"
          matRipple
        >
          <mat-icon matListItemIcon class="nav-icon">{{ item.icon }}</mat-icon>
          <span *ngIf="!isCollapsed" matListItemTitle class="nav-label">{{
            item.label
          }}</span>
          <span
            *ngIf="item.badge && item.badge > 0 && !isCollapsed"
            class="nav-badge"
            matListItemMeta
          >
            {{ item.badge }}
          </span>
        </mat-list-item>
      </ng-container>
    </mat-nav-list>
  </div>

  <!-- Settings Section -->
  <div class="settings-section">
    <mat-divider *ngIf="!isCollapsed"></mat-divider>

    <!-- Dark Mode Toggle -->
    <mat-nav-list class="settings-nav-list">
      <mat-list-item
        class="nav-item setting-item"
        [matTooltip]="isCollapsed ? 'Toggle Dark Mode' : ''"
        matTooltipPosition="right"
        (click)="isCollapsed ? toggleTheme() : null"
        [class.clickable]="isCollapsed"
        matRipple
      >
        <mat-icon matListItemIcon class="nav-icon">{{
          isDarkMode ? "light_mode" : "dark_mode"
        }}</mat-icon>
        <span *ngIf="!isCollapsed" matListItemTitle class="nav-label"
          >Dark Mode</span
        >
        <mat-slide-toggle
          *ngIf="!isCollapsed"
          [checked]="isDarkMode"
          (change)="onThemeToggleChange($event)"
          class="theme-toggle"
          color="primary"
          matListItemMeta
        ></mat-slide-toggle>
      </mat-list-item>
    </mat-nav-list>

    <!-- Profile Navigation Item -->
    <mat-nav-list class="profile-nav-list">
      <mat-list-item
        class="nav-item profile-item"
        [class.active]="
          isRouteActive('/settings/profile') ||
          isRouteActive('/settings/change-password')
        "
        (click)="navigateTo('/settings/profile')"
        [matTooltip]="isCollapsed ? 'Profile' : ''"
        matTooltipPosition="right"
        matRipple
      >
        <mat-icon matListItemIcon class="nav-icon">account_circle</mat-icon>
        <span *ngIf="!isCollapsed" matListItemTitle class="nav-label"
          >Profile</span
        >
      </mat-list-item>
    </mat-nav-list>
  </div>

  <!-- Logout Section -->
  <div class="logout-section">
    <mat-nav-list class="logout-nav-list">
      <mat-list-item
        class="nav-item logout-item"
        (click)="onLogout()"
        [matTooltip]="isCollapsed ? 'Logout' : ''"
        matTooltipPosition="right"
        matRipple
        aria-label="Logout"
      >
        <mat-icon matListItemIcon class="nav-icon">logout</mat-icon>
        <span *ngIf="!isCollapsed" matListItemTitle class="nav-label"
          >Logout</span
        >
      </mat-list-item>
    </mat-nav-list>
  </div>
</div>

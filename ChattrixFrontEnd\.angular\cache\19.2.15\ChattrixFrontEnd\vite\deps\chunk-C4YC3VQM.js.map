{"version": 3, "sources": ["../../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-transition-patch.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Input, Directive, NgModule } from '@angular/core';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * hack the bug\n * angular router change with unexpected transition trigger after calling applicationRef.attachView\n * https://github.com/angular/angular/issues/34718\n */\nclass NzTransitionPatchDirective {\n  elementRef;\n  renderer;\n  hidden = null;\n  setHiddenAttribute() {\n    if (this.hidden) {\n      if (typeof this.hidden === 'string') {\n        this.renderer.setAttribute(this.elementRef.nativeElement, 'hidden', this.hidden);\n      } else {\n        this.renderer.setAttribute(this.elementRef.nativeElement, 'hidden', '');\n      }\n    } else {\n      this.renderer.removeAttribute(this.elementRef.nativeElement, 'hidden');\n    }\n  }\n  constructor(elementRef, renderer) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.renderer.setAttribute(this.elementRef.nativeElement, 'hidden', '');\n  }\n  ngOnChanges() {\n    this.setHiddenAttribute();\n  }\n  ngAfterViewInit() {\n    this.setHiddenAttribute();\n  }\n  static ɵfac = function NzTransitionPatchDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTransitionPatchDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzTransitionPatchDirective,\n    selectors: [[\"\", \"nz-button\", \"\"], [\"nz-button-group\"], [\"\", \"nz-icon\", \"\"], [\"nz-icon\"], [\"\", \"nz-menu-item\", \"\"], [\"\", \"nz-submenu\", \"\"], [\"nz-select-top-control\"], [\"nz-select-placeholder\"], [\"nz-input-group\"]],\n    inputs: {\n      hidden: \"hidden\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTransitionPatchDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-button], nz-button-group, [nz-icon], nz-icon, [nz-menu-item], [nz-submenu], nz-select-top-control, nz-select-placeholder, nz-input-group'\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }], {\n    hidden: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTransitionPatchModule {\n  static ɵfac = function NzTransitionPatchModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTransitionPatchModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzTransitionPatchModule,\n    imports: [NzTransitionPatchDirective],\n    exports: [NzTransitionPatchDirective]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTransitionPatchModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzTransitionPatchDirective],\n      exports: [NzTransitionPatchDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzTransitionPatchDirective as ɵNzTransitionPatchDirective, NzTransitionPatchModule as ɵNzTransitionPatchModule };\n"], "mappings": ";;;;;;;;;;;;;;;AAYA,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,qBAAqB;AACnB,QAAI,KAAK,QAAQ;AACf,UAAI,OAAO,KAAK,WAAW,UAAU;AACnC,aAAK,SAAS,aAAa,KAAK,WAAW,eAAe,UAAU,KAAK,MAAM;AAAA,MACjF,OAAO;AACL,aAAK,SAAS,aAAa,KAAK,WAAW,eAAe,UAAU,EAAE;AAAA,MACxE;AAAA,IACF,OAAO;AACL,WAAK,SAAS,gBAAgB,KAAK,WAAW,eAAe,QAAQ;AAAA,IACvE;AAAA,EACF;AAAA,EACA,YAAY,YAAY,UAAU;AAChC,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,SAAS,aAAa,KAAK,WAAW,eAAe,UAAU,EAAE;AAAA,EACxE;AAAA,EACA,cAAc;AACZ,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAChB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,OAAO,OAAO,SAAS,mCAAmC,mBAAmB;AAC3E,WAAO,KAAK,qBAAqB,6BAA+B,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,CAAC;AAAA,EACtI;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,GAAG,CAAC,iBAAiB,GAAG,CAAC,IAAI,WAAW,EAAE,GAAG,CAAC,SAAS,GAAG,CAAC,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,cAAc,EAAE,GAAG,CAAC,uBAAuB,GAAG,CAAC,uBAAuB,GAAG,CAAC,gBAAgB,CAAC;AAAA,IACpN,QAAQ;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,0BAA0B;AAAA,IACpC,SAAS,CAAC,0BAA0B;AAAA,EACtC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,0BAA0B;AAAA,MACpC,SAAS,CAAC,0BAA0B;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}
import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil, interval, Subscription } from 'rxjs';

import { AuthService } from '../../Services/auth.service';
import { AuthStateService } from '../../Services/AuthState.service';
import { NotificationService } from '../../../../Core/Services/notification.service';
import {
  VerifyResetTokenRequest,
  ForgotPasswordRequest,
  AuthState,
} from '../../Models';

@Component({
  selector: 'app-verify-reset-token',
  standalone: false,
  templateUrl: './verify-reset-token.component.html',
  styleUrl: './verify-reset-token.component.scss',
})
export class VerifyResetTokenComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private timerSubscription?: Subscription;

  otpForm: FormGroup;
  email: string = '';
  logoLoaded = true; // Assume logo loads successfully by default

  // Timer state
  resendTimer = 0;
  canResend = false;

  // Authentication state
  isLoading = false;
  isResending = false;
  error: string | null = null;

  constructor(
    private authService: AuthService,
    private authState: AuthStateService,
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private notificationService: NotificationService,
  ) {
    this.otpForm = this.formBuilder.group({
      otpCode: [
        '',
        [
          Validators.required,
          Validators.pattern(/^\d{5}$/),
          Validators.minLength(5),
          Validators.maxLength(5),
        ],
      ],
    });
  }

  ngOnInit(): void {
    // Get email from query parameters
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe((params) => {
        this.email = params['email'];
        if (!this.email) {
          this.router.navigate(['/auth/forgot-password']);
          return;
        }
      });

    // Subscribe to authentication state
    this.authState.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: AuthState) => {
        this.isLoading = state.isLoading;
        this.error = state.error;

        // Error handling is now done centrally through interceptors
      });

    // Start resend timer
    this.startResendTimer();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.timerSubscription?.unsubscribe();
  }

  onOtpInput(event: any): void {
    const value = event.target.value;

    // Only allow numeric characters
    const numericValue = value.replace(/\D/g, '');

    // Limit to 5 digits
    const limitedValue = numericValue.slice(0, 5);

    // Update the form control
    this.otpForm.get('otpCode')?.setValue(limitedValue);

    // Auto-submit if 5 digits are entered
    if (limitedValue.length === 5) {
      setTimeout(() => this.onVerifyOtp(), 100);
    }
  }

  onOtpPaste(event: ClipboardEvent): void {
    event.preventDefault();
    const pastedText = event.clipboardData?.getData('text') || '';

    // Extract only numeric characters and limit to 5 digits
    const numericValue = pastedText.replace(/\D/g, '').slice(0, 5);

    // Update the form control
    this.otpForm.get('otpCode')?.setValue(numericValue);

    // Auto-submit if 5 digits are pasted
    if (numericValue.length === 5) {
      setTimeout(() => this.onVerifyOtp(), 100);
    }
  }

  onVerifyOtp(): void {
    // Clear any previous errors
    this.authState.clearError();

    const token = this.otpForm.get('otpCode')?.value || '';

    // Validate token format first
    if (!this.otpForm.valid || token.length !== 5 || !/^\d{5}$/.test(token)) {
      this.handleFormValidation();
      return;
    }

    const verificationData: VerifyResetTokenRequest = {
      email: this.email,
      resetToken: token,
    };

    this.authService.verifyResetToken(verificationData).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          this.notificationService.showSuccess(
            'Reset token verified successfully! You can now reset your password.',
          );
          // Navigate to reset password page with email and token
          this.router.navigate(['/auth/reset-password'], {
            queryParams: {
              email: this.email,
              token: token,
            },
          });
        }
      },
      error: (error: any) => {
        console.error('Token verification failed:', error);
        this.clearToken();
      },
    });
  }

  onVerifyToken(): void {
    // This method is called from the form submit, so we use onVerifyOtp instead
    this.onVerifyOtp();
  }

  onResendCode(): void {
    if (!this.canResend) return;

    this.isResending = true;
    const resendData: ForgotPasswordRequest = {
      email: this.email,
    };

    this.authService.forgotPassword(resendData).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          this.notificationService.showSuccess(
            'New verification code sent successfully! Please check your email.',
          );
          this.clearToken();
          this.startResendTimer();
        }
        this.isResending = false;
      },
      error: (error: any) => {
        console.error('Resend code failed:', error);
        this.isResending = false;
      },
    });
  }

  onBackToLogin(): void {
    this.router.navigate(['/auth/login']);
  }

  onImageError(): void {
    this.logoLoaded = false;
    console.log('Logo failed to load, showing fallback');
  }

  private clearToken(): void {
    this.otpForm.reset();
    this.otpForm.get('otpCode')?.setValue('');
  }

  private startResendTimer(): void {
    this.resendTimer = 60; // 60 seconds
    this.canResend = false;

    this.timerSubscription?.unsubscribe();
    this.timerSubscription = interval(1000)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.resendTimer--;
        if (this.resendTimer <= 0) {
          this.canResend = true;
          this.timerSubscription?.unsubscribe();
        }
      });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.otpForm.controls).forEach((key) => {
      const control = this.otpForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Handles form validation errors
   */
  private handleFormValidation(): void {
    this.markFormGroupTouched();

    const otpControl = this.otpForm.get('otpCode');

    if (otpControl?.hasError('required')) {
      this.notificationService.showError('Verification code is required.');
      return;
    }

    if (
      otpControl?.hasError('pattern') ||
      otpControl?.hasError('minlength') ||
      otpControl?.hasError('maxlength')
    ) {
      this.notificationService.showError(
        'Please enter a valid 5-digit verification code.',
      );
      return;
    }

    this.notificationService.showError(
      'Please enter a valid 5-digit verification code.',
    );
  }

  // Getter for timer display
  get timerDisplay(): string {
    const minutes = Math.floor(this.resendTimer / 60);
    const seconds = this.resendTimer % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}

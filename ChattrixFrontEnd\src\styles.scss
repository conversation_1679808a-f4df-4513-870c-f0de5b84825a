/* Chattrix Global Styles with Material UI Integration */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=SF+Pro+Text:wght@400;500;600;700&display=swap");
@import "@angular/material/prebuilt-themes/indigo-pink.css";

/* CSS Variables for Chattrix Color Palette - Black & White Minimalist */
:root {
  /* Primary Colors - Black & White Theme */
  --primary-dark: #000000;
  --primary-darker: #000000;
  --secondary-dark: #1a1a1a;
  --accent-primary: #000000;
  --accent-primary-hover: #333333;
  --accent-secondary: #ffffff;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-muted: #999999;
  --text-disabled: #666666;

  /* Background Colors */
  --bg-primary: #90747403;
  --bg-secondary: #625b5b;
  --bg-tertiary: #333333;
  --bg-card: #777171;
  --bg-input: #333333;
  --bg-hover: #404040;
  --bg-main-content: #f8f9fa; /* Light grayish-white for main content area */
  --bg-button: #000000;
  --bg-button-hover: #333333;

  /* Border Colors */
  --border-primary: #fafafa;
  --border-secondary: #666666;
  --border-focus: #ffffff;

  /* Status Colors */
  --success: #28a745;
  --error: #dc3545;
  --warning: #ffc107;
  --info: #17a2b8;

  /* Accent Colors */
  --accent-green: #28a745;

  /* Material UI Overrides */
  --mdc-theme-primary: #000000;
  --mdc-theme-secondary: #ffffff;
  --mdc-theme-surface: #1a1a1a;
  --mdc-theme-background: #000000;
  --mdc-theme-on-primary: #ffffff;
  --mdc-theme-on-secondary: #000000;
  --mdc-theme-on-surface: #ffffff;
  --mdc-theme-on-background: #ffffff;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md:
    0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg:
    0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl:
    0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Typography */
  --font-family:
    "Inter SemiBold", "SF Pro Display Medium", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Global Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Typography Classes */
.text-primary {
  color: var(--text-primary);
}
.text-secondary {
  color: var(--text-secondary);
}
.text-muted {
  color: var(--text-muted);
}
.text-disabled {
  color: var(--text-disabled);
}
.text-success {
  color: var(--success);
}
.text-error {
  color: var(--error);
}
.text-warning {
  color: var(--warning);
}
.text-info {
  color: var(--info);
}

/* Font Weight Classes */
.font-light {
  font-weight: 300;
}
.font-normal {
  font-weight: 400;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.font-bold {
  font-weight: 700;
}

/* Font Size Classes */
.text-xs {
  font-size: var(--font-size-xs);
}
.text-sm {
  font-size: var(--font-size-sm);
}
.text-base {
  font-size: var(--font-size-base);
}
.text-lg {
  font-size: var(--font-size-lg);
}
.text-xl {
  font-size: var(--font-size-xl);
}
.text-2xl {
  font-size: var(--font-size-2xl);
}
.text-3xl {
  font-size: var(--font-size-3xl);
}
.text-4xl {
  font-size: var(--font-size-4xl);
}

/* Material UI Form Field Overrides - Scoped to exclude authentication components */
:not(.auth-container)
  .mdc-text-field--outlined:not(.mdc-text-field--disabled)
  .mdc-notched-outline__leading,
:not(.auth-container)
  .mdc-text-field--outlined:not(.mdc-text-field--disabled)
  .mdc-notched-outline__notch,
:not(.auth-container)
  .mdc-text-field--outlined:not(.mdc-text-field--disabled)
  .mdc-notched-outline__trailing {
  border-color: var(--border-primary) !important;
}

/* Change the border hover color - Scoped to exclude authentication components */
:not(.auth-container)
  .mdc-text-field--outlined:not(.mdc-text-field--disabled):not(
    .mdc-text-field--focused
  ):hover
  .mdc-notched-outline
  .mdc-notched-outline__leading,
:not(.auth-container)
  .mdc-text-field--outlined:not(.mdc-text-field--disabled):not(
    .mdc-text-field--focused
  ):hover
  .mdc-notched-outline
  .mdc-notched-outline__notch,
:not(.auth-container)
  .mdc-text-field--outlined:not(.mdc-text-field--disabled):not(
    .mdc-text-field--focused
  ):hover
  .mdc-notched-outline
  .mdc-notched-outline__trailing {
  border-color: var(--border-secondary) !important;
}

/* Change the border focused color - Scoped to exclude authentication components */
:not(.auth-container)
  .mdc-text-field--outlined:not(
    .mdc-text-field--disabled
  ).mdc-text-field--focused
  .mdc-notched-outline__leading,
:not(.auth-container)
  .mdc-text-field--outlined:not(
    .mdc-text-field--disabled
  ).mdc-text-field--focused
  .mdc-notched-outline__notch,
:not(.auth-container)
  .mdc-text-field--outlined:not(
    .mdc-text-field--disabled
  ).mdc-text-field--focused
  .mdc-notched-outline__trailing {
  border-color: var(--border-focus) !important;
}

.mat-mdc-paginator-navigation-next,
.mat-mdc-paginator-navigation-previous,
.mat-mdc-paginator-navigation-first,
.mat-mdc-paginator-navigation-last {
  color: var(--text-primary) !important;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  background: var(--bg-secondary);
  width: 36px;
  height: 36px;
  margin: 0 var(--spacing-xs);

  &:hover:not([disabled]) {
    background: var(--bg-hover);
    color: var(--text-primary);
    border-color: var(--border-secondary);
  }

  &[disabled] {
    color: var(--text-muted);
    background: var(--bg-tertiary);
    border-color: var(--border-primary);
    opacity: 0.6;
    cursor: not-allowed;
  }

  .mat-mdc-button-touch-target {
    width: 36px;
    height: 36px;
  }

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

.mat-mdc-paginator-navigation-next .mat-mdc-button-touch-target,
.mat-mdc-paginator-navigation-previous .mat-mdc-button-touch-target,
.mat-mdc-paginator-navigation-first .mat-mdc-button-touch-target,
.mat-mdc-paginator-navigation-last .mat-mdc-button-touch-target {
  color: var(--text-primary) !important;
}

.mat-mdc-paginator-navigation-next,
.mat-mdc-paginator-navigation-previous,
.mat-mdc-paginator-navigation-first,
.mat-mdc-paginator-navigation-last.mat-mdc-icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  color: var(--text-primary) !important;
  transition:
    background-color var(--transition-normal),
    color var(--transition-normal);
}
.mat-mdc-paginator-range-actions {
  color: var(--text-primary) !important;
}

.mat-mdc-icon-button .mat-mdc-button-touch-target {
  color: var(--text-primary) !important;
}

.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label {
  color: var(
    --mat-tab-header-active-label-text-color,
    var(--mat-sys-on-surface)
  );
}

.mat-mdc-tab .mdc-tab__text-label {
  color: var(--text-primary) !important;
}

.mat-mdc-select {
  color: var(--text-primary) !important;
}

.mdc-text-field--no-label:not(.mdc-text-field--textarea)
  .mat-mdc-form-field-input-control.mdc-text-field__input,
.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control {
  color: var(--text-primary) !important;
}

/* Material UI Component Overrides for Black & White Theme - Scoped to exclude authentication components */
:not(.auth-container) .mat-mdc-form-field {
  --mdc-filled-text-field-container-color: var(--bg-input);
  --mdc-filled-text-field-label-text-color: var(--text-secondary);
  --mdc-filled-text-field-input-text-color: var(--text-primary);
  --mdc-filled-text-field-active-indicator-color: var(--accent-primary);
  --mdc-filled-text-field-focus-active-indicator-color: var(--accent-primary);
  --mdc-filled-text-field-hover-active-indicator-color: var(--accent-secondary);

  /* Reduce form field height and spacing */
  .mdc-text-field {
    height: 48px;
    width: 100%; /* Ensure 100% width by default */
    .collapsed {
      width: 100%;
    }
  }

  .mat-mdc-form-field-wrapper {
    padding-bottom: 0;
  }

  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 4px;
    min-height: 16px;
  }

  .mdc-floating-label :not(.mdc-floating-label--float-above) {
    // display: flex;
    // flex-direction: column;
    // align-items: center;
    // height: 100%;
    // padding-top: 2rem;
    // transform: translateY(-20%);
  }

  /* Fix label alignment for all form field types */
  .mat-mdc-form-field-infix {
    display: flex;
    align-items: center;
    min-height: 48px;
  }

  // /* Ensure labels are properly centered */
  .mdc-floating-label:not(.mdc-floating-label--float-above) {
    display: flex;
    align-items: center;
    height: 100%;
    top: 50%;
    transform: translateY(-50%);
  }

  // /* Fix for outline appearance */
  &.mat-mdc-form-field-appearance-outline {
    .mat-mdc-form-field-infix {
      padding-top: 12px;
      padding-bottom: 12px;
    }

    .mdc-floating-label {
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

/* Responsive width management for form fields - xxl breakpoint (1400px and above) */
// @media (min-width: 1440px) {
//   :not(.auth-container) .mat-mdc-form-field .mdc-text-field {
//     width: 550px; /* Fixed width for large screens */
//   }

//   /* Apply the same width to other form elements */
//   :not(.auth-container) .mat-mdc-select,
//   :not(.auth-container) .mat-mdc-form-field-type-mat-select .mdc-text-field,
//   :not(.auth-container) .mat-mdc-input-element,
//   :not(.auth-container) .mat-mdc-form-field-input-control {
//     width: 550px;
//   }
// }

.mat-mdc-paginator-navigation-next,
.mat-mdc-paginator-navigation-previous,
.mat-mdc-paginator-navigation-first,
.mat-mdc-paginator-navigation-last {
  color: var(--text-primary) !important;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  background: var(--bg-secondary);
  width: 36px;
  height: 36px;
  margin: 0 var(--spacing-xs);

  &:hover:not([disabled]) {
    background: var(--bg-hover);
    color: var(--text-primary);
    border-color: var(--border-secondary);
  }

  &[disabled] {
    color: var(--text-muted);
    background: var(--bg-tertiary);
    border-color: var(--border-primary);
    opacity: 0.6;
    cursor: not-allowed;
  }

  .mat-mdc-button-touch-target {
    width: 36px;
    height: 36px;
  }

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

.mat-mdc-paginator-navigation-next .mat-mdc-button-touch-target,
.mat-mdc-paginator-navigation-previous .mat-mdc-button-touch-target,
.mat-mdc-paginator-navigation-first .mat-mdc-button-touch-target,
.mat-mdc-paginator-navigation-last .mat-mdc-button-touch-target {
  color: var(--text-primary) !important;
}

.mat-mdc-paginator-navigation-next,
.mat-mdc-paginator-navigation-previous,
.mat-mdc-paginator-navigation-first,
.mat-mdc-paginator-navigation-last.mat-mdc-icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  color: var(--text-primary) !important;
  transition:
    background-color var(--transition-normal),
    color var(--transition-normal);
}
.mat-mdc-paginator-range-actions {
  color: var(--text-primary) !important;
}

.mat-mdc-icon-button .mat-mdc-button-touch-target {
  color: var(--text-primary) !important;
}

.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label {
  color: var(
    --mat-tab-header-active-label-text-color,
    var(--mat-sys-on-surface)
  );
}

.mat-mdc-tab .mdc-tab__text-label {
  color: var(--text-primary) !important;
}

.mat-mdc-select {
  color: var(--text-primary) !important;
}

.mdc-text-field--no-label:not(.mdc-text-field--textarea)
  .mat-mdc-form-field-input-control.mdc-text-field__input,
.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control {
  color: var(--text-primary) !important;
}

/* Material UI Button Overrides - Scoped to exclude authentication components */
:not(.auth-container) .mat-mdc-button.mat-primary {
  --mdc-filled-button-container-color: var(--accent-primary);
  --mdc-filled-button-label-text-color: var(--text-primary);
}

:not(.auth-container) .mat-mdc-button.mat-primary:hover {
  --mdc-filled-button-container-color: var(--accent-primary-hover);
}

.status-dropdown {
  position: relative;
  .mat-mdc-select-value-text {
    color: black;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px;
  }
  .mat-mdc-select-arrow-wrapper {
    display: none !important; /* Hide the dropdown arrow completely */
  }
  &:hover .mat-mdc-select-value {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    background: #c8e6c9;
    color: #1b5e20;
    border-radius: 16px;

    mat-icon {
      color: #1b5e20;
    }
  }
}
.add-user-btn {
  height: 44px;
  border-radius: var(--radius-md);
  font-weight: 500;
  text-transform: none;
  background: #000000 !important; /* Black background */
  color: #ffffff !important; /* White text */
  border: 2px solid #ffffff !important; /* White thick border */

  mat-icon {
    margin-right: var(--spacing-xs);
    color: var(--text-primary) !important;
  }

  &:hover {
    background: #333333 !important; /* Slightly lighter on hover */
    border-color: #ffffff !important;
  }
}

/* Shared Spinner Button Styles for User Management */
.spinner-button {
  position: relative;
  overflow: hidden;

  .button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    transition: all 0.2s ease-in-out;

    &.loading {
      .button-text {
        opacity: 0;
        visibility: hidden;
      }
    }

    .button-spinner {
      color: inherit;

      ::ng-deep circle {
        stroke: currentColor;
      }
    }

    .button-text {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-xs);
      transition:
        opacity 0.2s ease-in-out,
        visibility 0.2s ease-in-out;

      mat-icon {
        color: inherit;
        font-size: 18px;
        width: 18px;
        height: 18px;
      }

      span {
        color: inherit;
      }
    }
  }

  &:disabled {
    cursor: not-allowed;

    .button-content {
      .button-spinner {
        color: inherit;
      }
    }
  }
}

.mat-mdc-card {
  border-radius: 10px;
  --mdc-elevated-card-container-color: var(--bg-card);
  --mdc-elevated-card-container-shadow: var(--shadow-lg);
}

/* Additional Material UI Component Overrides for Black & White Theme */
.mat-mdc-table {
  --mdc-data-table-container-color: var(--bg-card);
  --mdc-data-table-row-item-label-text-color: var(--text-primary);
  --mdc-data-table-header-row-item-label-text-color: var(--text-secondary);
}

.mat-mdc-paginator {
  --mdc-filled-button-container-color: var(--accent-primary);
  --mdc-filled-button-label-text-color: var(--text-primary);
}

.mat-mdc-select {
  --mdc-filled-text-field-container-color: var(--bg-input);
  --mdc-filled-text-field-label-text-color: var(--text-secondary);
  --mdc-filled-text-field-input-text-color: var(--text-primary);
}

.mat-mdc-dialog-container {
  --mdc-dialog-container-color: var(--bg-card);
  --mdc-dialog-supporting-text-color: var(--text-primary);
  --mdc-dialog-subhead-color: var(--text-secondary);
}

.mat-mdc-snack-bar-container {
  --mdc-snackbar-container-color: var(--bg-card);
  --mdc-snackbar-supporting-text-color: var(--text-primary);
}

/* Minimalist Snackbar Styles */
.minimalist-snackbar {
  .mat-mdc-snack-bar-container {
    border-radius: 4px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    min-width: 320px !important;
    max-width: 480px !important;
    font-weight: 400 !important;
    border: 1px solid transparent !important;
    font-family: "Inter", sans-serif !important;
  }

  .mat-mdc-snack-bar-label {
    font-size: 14px !important;
    line-height: 1.5 !important;
    color: white !important;
  }

  .mat-mdc-snack-bar-action {
    font-weight: 500 !important;
    text-transform: none !important;
    letter-spacing: 0 !important;
    color: white !important;
    opacity: 0.9 !important;
  }

  .mat-mdc-snack-bar-action:hover {
    opacity: 1 !important;
  }
}

/* Legacy support for existing custom-snackbar class */
.custom-snackbar {
  .mat-mdc-snack-bar-container {
    border-radius: 4px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    min-width: 320px !important;
    max-width: 480px !important;
    font-weight: 400 !important;
    border: 1px solid transparent !important;
    font-family: "Inter", sans-serif !important;
  }

  .mat-mdc-snack-bar-label {
    font-size: 14px !important;
    line-height: 1.5 !important;
    color: white !important;
  }

  .mat-mdc-snack-bar-action {
    font-weight: 500 !important;
    text-transform: none !important;
    letter-spacing: 0 !important;
    color: white !important;
    opacity: 0.9 !important;
  }

  .mat-mdc-snack-bar-action:hover {
    opacity: 1 !important;
  }
}

/* Success Snackbar - Minimalist Green */
.success-snackbar {
  .mat-mdc-snack-bar-container {
    background-color: #22c55e !important;
    color: white !important;
    border-color: #16a34a !important;
  }

  .mat-mdc-snack-bar-action {
    color: white !important;
  }
}

/* Error Snackbar - Minimalist Red */
.error-snackbar {
  .mat-mdc-snack-bar-container {
    background-color: #ef4444 !important;
    color: white !important;
    border-color: #dc2626 !important;
  }

  .mat-mdc-snack-bar-action {
    color: white !important;
  }
}

/* Warning Snackbar */
.warning-snackbar {
  .mat-mdc-snack-bar-container {
    background-color: #ff9800 !important;
    color: white !important;
    border-color: #f57c00 !important;
  }

  .mat-mdc-snack-bar-action {
    color: white !important;
  }
}

/* Info Snackbar */
.info-snackbar {
  .mat-mdc-snack-bar-container {
    background-color: #2196f3 !important;
    color: white !important;
    border-color: #1976d2 !important;
  }

  .mat-mdc-snack-bar-action {
    color: white !important;
  }
}

/* Snackbar positioning and stacking */
.mat-mdc-snack-bar-container {
  margin-top: 8px !important;
}

/* Authentication theme override for snackbars */
.auth-page {
  .success-snackbar .mat-mdc-snack-bar-container {
    background-color: #4caf50 !important;
    color: white !important;
  }

  .error-snackbar .mat-mdc-snack-bar-container {
    background-color: #f44336 !important;
    color: white !important;
  }

  .warning-snackbar .mat-mdc-snack-bar-container {
    background-color: #ff9800 !important;
    color: white !important;
  }

  .info-snackbar .mat-mdc-snack-bar-container {
    background-color: #2196f3 !important;
    color: white !important;
  }
}

.mat-mdc-menu-panel {
  --mdc-menu-container-color: var(--bg-card);
  --mdc-list-list-item-label-text-color: var(--text-primary);
}

/* Legacy Authentication Layout Styles - DEPRECATED */
/* These styles are now replaced by the shared authentication theme */
/* Keeping them commented for reference but they should not be used */

/*
.auth-container {
  min-height: 100vh;
  min-height: 100dvh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e8e8e8;
  padding: var(--spacing-sm);
  box-sizing: border-box;
}

mat-card.auth-card {
  border-radius: 20px;
}

.auth-card {
  width: 100%;
  max-width: 400px;
  background: #ffffff;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  border: 2px solid #dbd9d9;
  overflow: hidden;
}

.auth-header {
  text-align: center;
  padding: var(--spacing-md) var(--spacing-lg) var(--spacing-sm);
  background: #ffffff;
}

.auth-logo {
  width: 110px;
  height: 110px;
  margin: 0 auto var(--spacing-md);
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #ffffff;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  margin-bottom: 20px;
}

.auth-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.auth-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: #ffffff;
  margin-bottom: var(--spacing-xs);
  line-height: var(--line-height-tight);
}

.auth-subtitle {
  font-size: var(--font-size-sm);
  color: #cccccc;
  margin-bottom: 0;
  line-height: var(--line-height-normal);
}

.auth-form {
  padding: var(--spacing-md) var(--spacing-lg);
  background: #ffffff;
}

.auth-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: center;
  border-top: 1px solid #333333;
  background: #ffffff;
}
*/

/* Mobile-First Responsive Design Enhancements */
@media (max-width: 768px) {
  .auth-container {
    padding: var(--spacing-sm);
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile browsers */
  }

  .auth-card {
    margin: 0;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
  }

  .auth-header {
    padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-md);
  }

  .auth-form {
    padding: var(--spacing-lg);
  }

  .auth-footer {
    padding: var(--spacing-md) var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .auth-container {
    padding: var(--spacing-xs);
  }

  .auth-card {
    border-radius: var(--radius-md);
  }

  .auth-header {
    padding: var(--spacing-lg) var(--spacing-md) var(--spacing-sm);
  }

  .auth-form {
    padding: var(--spacing-md);
  }

  .auth-footer {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .auth-title {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-tight);
  }

  .auth-subtitle {
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
  }
}

/* Touch-Friendly Interactions */
@media (hover: none) and (pointer: coarse) {
  /* Increase touch targets for mobile */
  .mat-mdc-button {
    min-height: 44px;
    min-width: 44px;
  }

  .mat-mdc-icon-button {
    width: 44px;
    height: 44px;
  }

  /* Remove hover effects on touch devices */
  .mat-mdc-button:hover {
    background-color: inherit;
  }

  /* Improve form field touch targets */
  .mat-mdc-form-field .mat-mdc-text-field-wrapper {
    min-height: 48px;
  }
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .auth-logo img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Landscape Orientation on Mobile */
@media (max-height: 500px) and (orientation: landscape) {
  .auth-container {
    padding: var(--spacing-xs);
  }

  .auth-card {
    max-height: 90vh;
    overflow-y: auto;
  }

  .auth-header {
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-sm);
  }

  .auth-logo {
    width: 50px;
    height: 50px;
    margin-bottom: var(--spacing-sm);
  }

  .auth-title {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
  }

  .auth-subtitle {
    font-size: var(--font-size-xs);
    margin-bottom: var(--spacing-xs);
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark Mode Support (if system preference changes) */
@media (prefers-color-scheme: dark) {
  /* Our app is already dark-themed, but we can add specific adjustments here if needed */
  :root {
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md:
      0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg:
      0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    --shadow-xl:
      0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  }
}

html,
body {
  height: 100%;
}
body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

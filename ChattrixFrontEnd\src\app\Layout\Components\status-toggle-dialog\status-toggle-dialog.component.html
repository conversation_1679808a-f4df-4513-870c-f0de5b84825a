<div class="status-toggle-dialog">
  <!-- Dialog Header -->
  <div class="dialog-header">
    <h2 mat-dialog-title class="dialog-title">
      <mat-icon [class]="actionClass + '-icon'">{{ actionIcon }}</mat-icon>
      {{ actionText.charAt(0).toUpperCase() + actionText.slice(1) }} User
    </h2>
  </div>

  <!-- Dialog Content -->
  <div mat-dialog-content class="dialog-content">
    <!-- User Information -->
    <div class="user-info-section">
      <div class="user-summary">
        <!-- Profile Picture with Fallback -->
        <div
          class="user-avatar"
          [class.has-image]="getProfilePictureUrl(data.user)"
        >
          <img
            *ngIf="getProfilePictureUrl(data.user)"
            [src]="getProfilePictureUrl(data.user)"
            [alt]="getUserDisplayName()"
            class="avatar-image"
            #avatarImg
            (error)="avatarImg.style.display = 'none'"
          />
          <span
            *ngIf="!getProfilePictureUrl(data.user)"
            class="avatar-initials"
          >
            {{ getUserInitials(data.user) }}
          </span>
        </div>
        <div class="user-details">
          <h3 class="user-name">{{ getUserDisplayName() }}</h3>
          <p class="user-email">{{ data.user.email }}</p>
          <p class="user-roles">{{ getUserRoleDisplay() }}</p>
        </div>
      </div>
    </div>

    <!-- Confirmation Message -->
    <div class="confirmation-section">
      <div class="confirmation-card">
        <div class="confirmation-content">
          <mat-icon [class]="actionClass + '-icon'">{{ actionIcon }}</mat-icon>
          <div class="confirmation-text">
            <h4>Are you sure you want to {{ actionText }} this user?</h4>
            <p *ngIf="data.user.isActive" class="status-warning">
              This user will no longer be able to access the system.
            </p>
            <p *ngIf="!data.user.isActive" class="status-info">
              This user will regain access to the system.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Dialog Actions -->
  <div mat-dialog-actions class="dialog-actions">
    <button mat-button type="button" (click)="onCancel()" class="cancel-button">
      Cancel
    </button>
    <button
      mat-raised-button
      type="button"
      (click)="onConfirm()"
      [class]="actionClass + '-button'"
    >
      <mat-icon>{{ actionIcon }}</mat-icon>
      {{ actionText.charAt(0).toUpperCase() + actionText.slice(1) }}
    </button>
  </div>
</div>

import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import { AuthenticationService } from '../../app/Pages/authentication/Services/Authentication.service';
import { AuthStateService } from '../../app/Pages/authentication/Services/AuthState.service';
import { FileUploadService } from '../../app/Pages/authentication/Services/FileUpload.service';
import { NotificationService } from '../../app/Core/Services/notification.service';
import {
  RegisterRequest,
  AuthError,
  UserInfo,
} from '../../app/Pages/authentication/Models';

@Component({
  selector: 'app-profile-picture',
  standalone: false,
  templateUrl: './profile-picture.component.html',
  styleUrl: './profile-picture.component.scss',
})
export class ProfilePictureComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  profileForm: FormGroup;
  logoLoaded = true;
  selectedFile: File | null = null;
  imagePreview: string | null = null;
  isLoading = false;

  // File validation
  maxFileSize = 5 * 1024 * 1024; // 5MB
  allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];

  constructor(
    private authService: AuthenticationService,
    private authState: AuthStateService,
    private fileUploadService: FileUploadService,
    private formBuilder: FormBuilder,
    private router: Router,
    private notificationService: NotificationService,
  ) {
    this.profileForm = this.formBuilder.group({
      description: ['', [Validators.maxLength(500)]],
    });
  }

  ngOnInit(): void {
    // Check if we have pending signup data
    const pendingData = sessionStorage.getItem('pendingSignupData');
    if (!pendingData) {
      // If no pending data, redirect to signup
      this.router.navigate(['/auth/signup']);
      return;
    }

    // Subscribe to auth state changes
    this.authState.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state) => {
        this.isLoading = state.isLoading;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file using service
      const validation = this.fileUploadService.validateFile(file);
      if (!validation.isValid) {
        this.showError(validation.error!);
        return;
      }

      this.selectedFile = file;

      // Create image preview using service
      this.fileUploadService
        .createImagePreview(file)
        .then((preview) => {
          this.imagePreview = preview;
        })
        .catch((error) => {
          console.error('Error creating preview:', error);
          this.showError('Failed to create image preview.');
        });
    }
  }

  removeImage(event?: Event): void {
    // Prevent event bubbling to avoid triggering file input
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    this.selectedFile = null;
    this.imagePreview = null;
    // Reset file input
    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  triggerFileInput(event?: Event): void {
    // Prevent default behavior for keyboard events
    if (event) {
      event.preventDefault();
    }

    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }

  onCompleteProfile(): void {
    if (this.profileForm.valid) {
      const pendingData = sessionStorage.getItem('pendingSignupData');
      if (!pendingData) {
        this.showError('Session expired. Please start over.');
        this.router.navigate(['/auth/signup']);
        return;
      }

      const signupData = JSON.parse(pendingData);

      // Create FormData using service
      const formData = this.fileUploadService.prepareRegistrationFormData(
        signupData,
        this.profileForm.value.description || '',
        this.selectedFile || undefined,
      );

      this.isLoading = true;

      // Call the registration API with FormData
      this.registerWithProfile(formData);
    } else {
      this.markFormGroupTouched();
    }
  }

  private registerWithProfile(formData: FormData): void {
    this.fileUploadService.registerWithProfile(formData).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          // Clear pending data
          sessionStorage.removeItem('pendingSignupData');

          this.showSuccess(
            'Account created successfully! Please check your email for verification.',
          );

          // Navigate to OTP verification if required
          if (response.data?.userId) {
            this.router.navigate(['/auth/otp-verification'], {
              queryParams: { userId: response.data.userId },
            });
          } else {
            this.router.navigate(['/auth/login']);
          }
        }
      },
      error: (error) => {
        // Errors are now handled by the AuthNotificationService in the FileUploadService
        console.error('Registration failed:', error);
        this.isLoading = false;
      },
    });
  }

  onBack(): void {
    this.router.navigate(['/auth/signup']);
  }

  onImageError(event: any): void {
    this.logoLoaded = false;
    console.log('Logo failed to load, showing fallback');
  }

  private markFormGroupTouched(): void {
    Object.keys(this.profileForm.controls).forEach((key) => {
      const control = this.profileForm.get(key);
      control?.markAsTouched();
    });
  }

  private showError(message: string): void {
    this.notificationService.showError(message);
  }

  private showSuccess(message: string): void {
    this.notificationService.showSuccess(message);
  }

  // Getter methods for template
  get descriptionControl() {
    return this.profileForm.get('description');
  }
}

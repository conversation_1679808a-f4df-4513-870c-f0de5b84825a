using AutoMapper;
using ChattrixBackend.Core.Entities.UserManagement.ChangePasswordModel;
using ChattrixBackend.Core.Entities.UserManagement.EmailServiceModel;
using ChattrixBackend.Core.Entities.UserManagement.ForgotPasswordModel;
using ChattrixBackend.Core.Entities.UserManagement.LoginModel;
using ChattrixBackend.Core.Entities.UserManagement.RegisterModel;
using ChattrixBackend.Core.Entities.UserManagement.ResetPasswordModel;
using ChattrixBackend.Core.Entities.UserManagement.ResponseModel;
using ChattrixBackend.Core.Entities.UserManagement.ToggleStatusRequestModel;
using ChattrixBackend.Core.Entities.UserManagement.UpdateProfileRequestModel;
using ChattrixBackend.Core.Entities.UserManagement.UserDetailsModel;
using ChattrixBackend.Core.Entities.UserManagement.UserModel;
using ChattrixBackend.Core.Entities.UserManagement.VerifyOtpRequestModel;
using ChattrixBackend.Core.Entities.UserManagement.VerifyResetTokenModel;
using ChattrixBackend.Core.Pagination.PagedResponseModel;
using ChattrixBackend.Core.Pagination.PaginationParametersModel;
using ChattrixBackend.Services.OtpServices;
using ChattrixBackend.Services.PasswordResetServices;
using ChattrixBackend.Services.S3Services;
using Identity.Services.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;


namespace ChattrixBackend.Services.AccountServices {
    public class AccountService : IAccountService {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly IConfiguration _configuration;
        private readonly IEmailService _emailService;
        private readonly IOtpService _otpService;
        private readonly IPasswordResetService _passwordResetService;
        private readonly IMapper _mapper;
        private readonly IS3Service _s3Service;

        public AccountService(
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole> roleManager,
            IConfiguration configuration,
            IEmailService emailService,
            IOtpService otpService,
            IPasswordResetService passwordResetService,
            IMapper mapper, IS3Service s3Service) {
            _userManager = userManager;
            _roleManager = roleManager;
            _configuration = configuration;
            _emailService = emailService;
            _otpService = otpService;
            _passwordResetService = passwordResetService;
            _mapper = mapper;
            _s3Service = s3Service;
        }

        public async Task<Response> AddUserAsync(AddUser register) {
            var existingUser = await _userManager.FindByEmailAsync(register.Email);
            if (existingUser != null) {
                return new Response {
                    IsSuccess = false,
                    Message = "A user with this email already exists"
                };
            }

            // Use AutoMapper to map Register to ApplicationUser
            var user = _mapper.Map<ApplicationUser>(register);

            // Ensure ProfilePictureUrl is not null
            if (string.IsNullOrEmpty(user.ProfilePictureUrl)) {
                user.ProfilePictureUrl = ""; // Set to empty string instead of null
            }

            var result = await _userManager.CreateAsync(user, register.Password);
            if (!result.Succeeded) {
                return new Response {
                    IsSuccess = false,
                    Message = "Failed to create user",
                    Data = result.Errors
                };
            }

            if (register.Roles != null && register.Roles.Any()) {
                foreach (var role in register.Roles) {
                    if (!await _roleManager.RoleExistsAsync(role)) {
                        await _roleManager.CreateAsync(new IdentityRole(role));
                    }
                }
                await _userManager.AddToRolesAsync(user, register.Roles);
            }

            return new Response {
                IsSuccess = true,
                Message = "User created successfully"
            };
        }

        public async Task<Response> CreateAdminUserAsync(AddUser register) {
            var existingUser = await _userManager.FindByEmailAsync(register.Email);
            if (existingUser != null) {
                return new Response {
                    IsSuccess = false,
                    Message = "A user with this email already exists"
                };
            }
            // Use AutoMapper to map Register to ApplicationUser
            var user = _mapper.Map<ApplicationUser>(register);
            user.IsActive = true; // Ensure admin user is active

            // Ensure ProfilePictureUrl is not null
            if (string.IsNullOrEmpty(user.ProfilePictureUrl)) {
                user.ProfilePictureUrl = ""; // Set to empty string instead of null
            }

            var result = await _userManager.CreateAsync(user, register.Password);
            if (!result.Succeeded) {
                return new Response {
                    IsSuccess = false,
                    Message = "Failed to create admin user",
                    Data = result.Errors
                };
            }
            // Assign admin role
            if (!await _roleManager.RoleExistsAsync("Super Admin")) {
                await _roleManager.CreateAsync(new IdentityRole("Super Admin"));
            }
            await _userManager.AddToRoleAsync(user, "Super Admin");
            return new Response {
                IsSuccess = true,
                Message = "Admin user created successfully"
            };
        }

        public async Task<Response> RegisterUserAsync(AddUser register) {
            var existingUser = await _userManager.FindByEmailAsync(register.Email);
            if (existingUser != null) {
                return new Response {
                    IsSuccess = false,
                    Message = "A user with this email already exists"
                };
            }
            // Use AutoMapper to map Register to ApplicationUser
            var user = _mapper.Map<ApplicationUser>(register);
            user.IsActive = true; // Ensure admin user is active

            // Ensure ProfilePictureUrl is not null
            if (string.IsNullOrEmpty(user.ProfilePictureUrl)) {
                user.ProfilePictureUrl = ""; // Set to empty string instead of null
            }

            var result = await _userManager.CreateAsync(user, register.Password);
            if (!result.Succeeded) {
                return new Response {
                    IsSuccess = false,
                    Message = "Failed to create admin user",
                    Data = result.Errors
                };
            }
            // Assign admin role
            if (!await _roleManager.RoleExistsAsync("User")) {
                await _roleManager.CreateAsync(new IdentityRole("User"));
            }
            await _userManager.AddToRoleAsync(user, "User");
            return new Response {
                IsSuccess = true,
                Message = "User created successfully"
            };
        }


        public async Task<Response> UpdateUserAsync(string userId, UserDetails userDetails) {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null) {
                return new Response {
                    IsSuccess = false,
                    Message = "User not found"
                };
            }

            user.FullName = userDetails.FullName ?? user.FullName;
            user.PhoneNumber = userDetails.PhoneNumber ?? user.PhoneNumber;
            user.Description = userDetails.Description ?? user.Description;
            user.IsActive = userDetails.IsActive;


            var result = await _userManager.UpdateAsync(user);
            if (!result.Succeeded) {
                return new Response {
                    IsSuccess = false,
                    Message = "Failed to update user",
                    Data = result.Errors
                };
            }


            if (userDetails.Roles != null && userDetails.Roles.Any()) {
                var currentRoles = await _userManager.GetRolesAsync(user);
                var rolesToAdd = userDetails.Roles.Except(currentRoles).ToList();
                var rolesToRemove = currentRoles.Except(userDetails.Roles).ToList();

                if (rolesToAdd.Any()) {
                    await _userManager.AddToRolesAsync(user, rolesToAdd);
                }

                if (rolesToRemove.Any()) {
                    await _userManager.RemoveFromRolesAsync(user, rolesToRemove);
                }
            }

            return new Response {
                IsSuccess = true,
                Message = "User updated successfully"
            };
        }

        public async Task<Response> UpdateProfileAsync(string userId, UpdateProfileRequest request) {
            try {
                if (string.IsNullOrEmpty(userId)) {
                    return new Response {
                        IsSuccess = false,
                        Message = "User ID is required"
                    };
                }

                if (request == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Update request is required"
                    };
                }

                var user = await _userManager.FindByIdAsync(userId);
                if (user == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "User not found"
                    };
                }

                // Check if at least one field is provided for update
                bool hasUpdates = !string.IsNullOrWhiteSpace(request.FullName) ||
                                !string.IsNullOrWhiteSpace(request.PhoneNumber) ||
                                !string.IsNullOrWhiteSpace(request.Description) ||
                                !string.IsNullOrWhiteSpace(request.ProfileImageUrl);

                if (!hasUpdates) {
                    return new Response {
                        IsSuccess = false,
                        Message = "At least one field must be provided for update"
                    };
                }

                // Update profile fields if provided
                if (!string.IsNullOrWhiteSpace(request.FullName)) {
                    user.FullName = request.FullName.Trim();
                }

                if (!string.IsNullOrWhiteSpace(request.PhoneNumber)) {
                    user.PhoneNumber = request.PhoneNumber.Trim();
                }

                if (!string.IsNullOrWhiteSpace(request.Description)) {
                    user.Description = request.Description.Trim();
                }

                if (!string.IsNullOrWhiteSpace(request.ProfileImageUrl)) {
                    // Delete old profile image if exists and is different
                    if (!string.IsNullOrEmpty(user.ProfilePictureUrl) &&
                        user.ProfilePictureUrl != request.ProfileImageUrl) {
                        try {
                            DeleteFile(user.ProfilePictureUrl);
                        }
                        catch (Exception ex) {
                            // Log the error but don't fail the update
                            Console.WriteLine($"Warning: Failed to delete old profile image: {ex.Message}");
                        }
                    }
                    user.ProfilePictureUrl = request.ProfileImageUrl;
                }

                var result = await _userManager.UpdateAsync(user);

                if (result.Succeeded) {
                    return new Response {
                        IsSuccess = true,
                        Message = "Profile updated successfully"
                    };
                }

                // Collect error messages from Identity result
                var errors = string.Join("; ", result.Errors.Select(e => e.Description));
                return new Response {
                    IsSuccess = false,
                    Message = $"Failed to update profile: {errors}"
                };
            }
            catch (Exception ex) {
                return new Response {
                    IsSuccess = false,
                    Message = $"An error occurred while updating profile: {ex.Message}"
                };
            }
        }

        public async Task<Response> DeleteUserAsync(string userId, ToggleStatusRequest request, string currentUserId) {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null) {
                return new Response {
                    IsSuccess = false,
                    Message = "User not found"
                };
            }

            // Check if user is trying to deactivate themselves
            if (userId == currentUserId && !request.IsActive) {
                return new Response {
                    IsSuccess = false,
                    Message = "You cannot Delete your own account"
                };
            }

            user.IsActive = request.IsActive;
            var result = await _userManager.UpdateAsync(user);

            if (result.Succeeded) {
                return new Response {
                    IsSuccess = true,
                    Message = "User Deleted successfully"
                };
            }

            return new Response {
                IsSuccess = false,
                Message = "Failed to Delete User"
            };
        }



        public async Task<Response> DeleteSQlUser(string userId) {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null) {
                return new Response {
                    IsSuccess = false,
                    Message = "User not found"
                };
            }

            await _userManager.DeleteAsync(user);
            return new Response {
                IsSuccess = true,
                Message = "User deleted successfully"
            };


        }

        public async Task<Response> LoginAsync(Login login) {
            var user = await _userManager.FindByEmailAsync(login.Email);
            if (user == null) {
                return new Response {
                    IsSuccess = false,
                    Message = "User not found with this email"
                };
            }

            var isPasswordValid = await _userManager.CheckPasswordAsync(user, login.Password);
            if (!isPasswordValid) {
                return new Response {
                    IsSuccess = false,
                    Message = "Invalid password"
                };
            }

            // Check if user is active
            if (!user.IsActive) {
                return new Response {
                    IsSuccess = false,
                    Message = "Your account is Deleted. Please contact an administrator to activate your account."
                };
            }

            // TEMPORARY: Skip OTP for testing purposes
            var skipOtp = user.Email?.Contains("<EMAIL>") == true;

            var recentlyVerified = await _otpService.IsOtpRecentlyVerified(user.Id);
            if (!recentlyVerified && !skipOtp) {
                var otp = GenerateOtp();
                var message = new Message(
                    new[] { user.Email ?? string.Empty },
                    "Your OTP Code",
                    $"Your OTP code is: {otp}. It is valid for 5 min."
                );
                _emailService.SendEmail(message);
                await _otpService.StoreOtp(user.Id, otp);
                Console.WriteLine($"required otp is: {otp}");

                return new Response {
                    IsSuccess = true,
                    Message = "OTP sent to your email. Please verify.",
                    Data = new { RequiresOtp = true, UserId = user.Id }
                };
            }

            var token = GenerateToken(user);
            return new Response {
                IsSuccess = true,
                Message = "Login successful",
                Token = token,
                Data = new { RequiresOtp = false, UserId = user.Id }
            };
        }

        public async Task<Response> VerifyOtpAsync(VerifyOtpRequest request) {
            var user = await _userManager.FindByIdAsync(request.UserId);
            if (user == null) {
                return new Response {
                    IsSuccess = false,
                    Message = "User not found"
                };
            }

            // Check if user is active
            if (!user.IsActive) {
                return new Response {
                    IsSuccess = false,
                    Message = "Your account is inactive. Please contact an administrator to activate your account."
                };
            }

            var isValid = await _otpService.VerifyOtp(user.Id, request.Otp);
            if (!isValid) {
                return new Response {
                    IsSuccess = false,
                    Message = "Invalid or expired OTP"
                };
            }

            await _otpService.MarkOtpVerified(user.Id);
            var token = GenerateToken(user);

            return new Response {
                IsSuccess = true,
                Message = "OTP verified, login successful",
                Token = token
            };
        }

        public async Task<UserDetails?> GetUserByIdAsync(string userId) {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null) return null;

            var roles = await _userManager.GetRolesAsync(user);


            return new UserDetails {
                Id = user.Id,
                FullName = user.FullName,
                Email = user.Email,
                PhoneNumber = user.PhoneNumber,
                Description = user.Description,
                ProfileImageUrl = user.ProfilePictureUrl,
                IsActive = user.IsActive,
                CreatedOn = user.CreatedOn,
                Roles = roles.ToList()
            };
        }

        public async Task<List<UserDetails>> GetAllUsersAsync() {
            var users = _userManager.Users.ToList();
            var result = new List<UserDetails>();

            foreach (var user in users) {
                var roles = await _userManager.GetRolesAsync(user);


                result.Add(new UserDetails {
                    Id = user.Id,
                    FullName = user.FullName,
                    Email = user.Email,
                    PhoneNumber = user.PhoneNumber,
                    Description = user.Description,
                    IsActive = user.IsActive,
                    CreatedOn = user.CreatedOn,
                    Roles = roles.ToList()
                });
            }

            return result;
        }


        public async Task<PagedResponse<UserDetails>> GetPagedUsersAsync(PaginationParameters parameters) {
            try {
                // Start with the base query
                var query = _userManager.Users.AsQueryable();

                // Apply non-role filters first
                if (!string.IsNullOrWhiteSpace(parameters.Name))
                    query = query.Where(u => u.FullName.Contains(parameters.Name));


                if (!string.IsNullOrWhiteSpace(parameters.Status)) {
                    var isActive = parameters.Status.ToLower() == "active";
                    query = query.Where(u => u.IsActive == isActive);
                }

                // Apply sorting
                if (!string.IsNullOrWhiteSpace(parameters.SortField)) {
                    var isAscending = parameters.SortOrder?.ToLower() != "desc";
                    query = ApplySorting(query, parameters.SortField, isAscending);
                }
                else {
                    query = query.OrderByDescending(u => u.CreatedOn);
                }

                // Execute the query to get the initial list of users
                var users = await query.ToListAsync();

                // Filter by role if specified (in memory)
                if (!string.IsNullOrWhiteSpace(parameters.Role)) {
                    var filteredUsers = new List<ApplicationUser>();
                    foreach (var user in users) {
                        var userRoles = await _userManager.GetRolesAsync(user);
                        if (userRoles.Any(r => r.Equals(parameters.Role, StringComparison.OrdinalIgnoreCase))) {
                            filteredUsers.Add(user);
                        }
                    }
                    users = filteredUsers;
                }

                // Calculate total items after role filtering
                var totalItems = users.Count;

                // Apply pagination after role filtering
                var pagedUsers = users
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToList();

                // Map to UserDetails
                var userDetailsList = new List<UserDetails>();
                foreach (var user in pagedUsers) {
                    var roles = await _userManager.GetRolesAsync(user);
                    userDetailsList.Add(new UserDetails {
                        Id = user.Id,
                        FullName = user.FullName,
                        Email = user.Email,
                        PhoneNumber = user.PhoneNumber,
                        Description = user.Description,
                        IsActive = user.IsActive,
                        CreatedOn = user.CreatedOn,
                        ProfileImageUrl = user.ProfilePictureUrl,
                        Roles = roles.ToList()
                    });
                }

                return new PagedResponse<UserDetails>(userDetailsList, totalItems, parameters.PageNumber, parameters.PageSize);
            }
            catch (Exception ex) {
                // Log the exception details here if you have logging configured
                throw; // Re-throw the exception to be handled by the controller
            }
        }

        private IQueryable<ApplicationUser> ApplySorting(IQueryable<ApplicationUser> query, string sortField, bool isAscending) {
            return sortField.ToLower() switch {
                "fullname" => isAscending ? query.OrderBy(u => u.FullName) : query.OrderByDescending(u => u.FullName),
                "email" => isAscending ? query.OrderBy(u => u.Email) : query.OrderByDescending(u => u.Email),
                "createdon" => isAscending ? query.OrderBy(u => u.CreatedOn) : query.OrderByDescending(u => u.CreatedOn),
                "isactive" => isAscending ? query.OrderBy(u => u.IsActive) : query.OrderByDescending(u => u.IsActive),
                _ => query.OrderByDescending(u => u.CreatedOn)
            };
        }

        private string GenerateToken(ApplicationUser user) {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(_configuration["JWTSetting:securityKey"]);

            var roles = _userManager.GetRolesAsync(user).Result;
            var claims = new List<Claim>
            {
                new(JwtRegisteredClaimNames.Email, user.Email ?? ""),
                new(JwtRegisteredClaimNames.Name, user.FullName ?? ""),
                new(ClaimTypes.NameIdentifier, user.Id ?? ""), // Use NameIdentifier instead of NameId
                new(JwtRegisteredClaimNames.Sub, user.Id ?? ""), // Add Subject claim
                // Remove manual Aud and Iss claims - let TokenDescriptor handle them
                // Add custom claims for additional user data
                new("PhoneNumber", user.PhoneNumber ?? ""),
                new("Description", user.Description ?? ""),
                new("ProfilePictureUrl", user.ProfilePictureUrl ?? ""),
                new("IsActive", user.IsActive.ToString())
            };

            claims.AddRange(roles.Select(role => new Claim(ClaimTypes.Role, role)));

            var tokenDescriptor = new SecurityTokenDescriptor {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddDays(1),
                Issuer = _configuration["JWTSetting:ValidIssuer"],
                Audience = _configuration["JWTSetting:ValidAudience"],
                SigningCredentials = new SigningCredentials(
                    new SymmetricSecurityKey(key),
                    SecurityAlgorithms.HmacSha256
                )
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        private string GenerateOtp() {
            var random = new Random();
            return random.Next(10000, 99999).ToString();
        }

        public async Task<Response> ChangePasswordAsync(ChangePasswordRequest request) {
            var user = await _userManager.FindByIdAsync(request.UserId);
            if (user == null) {
                return new Response {
                    IsSuccess = false,
                    Message = "User not found"
                };
            }

            // Verify the current password
            var isCurrentPasswordValid = await _userManager.CheckPasswordAsync(user, request.CurrentPassword);
            if (!isCurrentPasswordValid) {
                return new Response {
                    IsSuccess = false,
                    Message = "Current password is incorrect"
                };
            }

            // Change the password
            var result = await _userManager.ChangePasswordAsync(user, request.CurrentPassword, request.NewPassword);
            if (!result.Succeeded) {
                return new Response {
                    IsSuccess = false,
                    Message = "Failed to change password",
                    Data = result.Errors
                };
            }

            return new Response {
                IsSuccess = true,
                Message = "Password changed successfully"
            };
        }

        public async Task<Response> ResendOtpAsync(string userId) {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null) {
                return new Response {
                    IsSuccess = false,
                    Message = "User not found"
                };
            }

            // Check if user is active
            if (!user.IsActive) {
                return new Response {
                    IsSuccess = false,
                    Message = "Your account is Deleted. Please contact an administrator to activate your account."
                };
            }

            // Generate a new OTP
            var otp = GenerateOtp();

            // Store the OTP
            await _otpService.StoreOtp(user.Id, otp);

            // Send the OTP via email
            var message = new Message(
                new[] { user.Email ?? string.Empty },
                "Your New OTP Code",
                $"Your new OTP code is: {otp}. It is valid for 5 min."
            );
            _emailService.SendEmail(message);

            return new Response {
                IsSuccess = true,
                Message = "New OTP sent to your email. Please verify.",
                Data = new { UserId = user.Id }
            };
        }

        public async Task<Response> InitiateForgotPasswordAsync(ForgotPasswordRequest request) {
            var user = await _userManager.FindByEmailAsync(request.Email);
            if (user == null) {
                // Don't reveal that the user doesn't exist for security reasons
                return new Response {
                    IsSuccess = true,
                    Message = "If an account with this email exists, a password reset code has been sent."
                };
            }

            // Check if user is active
            if (!user.IsActive) {
                return new Response {
                    IsSuccess = false,
                    Message = "Your account is Delted. Please contact an administrator."
                };
            }

            // Check rate limiting
            var canRequest = await _passwordResetService.CanRequestResetAsync(request.Email);
            if (!canRequest) {
                return new Response {
                    IsSuccess = false,
                    Message = "Please wait 5 minutes before requesting another password reset."
                };
            }

            // Generate reset token
            var resetToken = await _passwordResetService.GenerateResetTokenAsync(user.Id, request.Email);

            // Send reset email
            var message = new Message(
                new[] { user.Email ?? string.Empty },
                "Password Reset Code",
                $"Your password reset code is: {resetToken}. This code will expire in 15 minutes."
            );
            _emailService.SendEmail(message);

            return new Response {
                IsSuccess = true,
                Message = "If an account with this email exists, a password reset code has been sent."
            };
        }

        public async Task<Response> VerifyResetTokenAsync(VerifyResetTokenRequest request) {
            var isValid = await _passwordResetService.VerifyResetTokenAsync(request.Email, request.ResetToken);

            if (!isValid) {
                return new Response {
                    IsSuccess = false,
                    Message = "Invalid or expired reset code."
                };
            }

            return new Response {
                IsSuccess = true,
                Message = "Reset code verified successfully."
            };
        }

        public async Task<Response> ResetPasswordAsync(ResetPasswordRequest request) {
            var user = await _userManager.FindByEmailAsync(request.Email);
            if (user == null) {
                return new Response {
                    IsSuccess = false,
                    Message = "Invalid request."
                };
            }

            // Verify the reset token
            var isTokenValid = await _passwordResetService.IsTokenValidAsync(request.Email, request.ResetToken);
            if (!isTokenValid) {
                return new Response {
                    IsSuccess = false,
                    Message = "Invalid or expired reset code."
                };
            }

            // Reset the password
            var resetToken = await _userManager.GeneratePasswordResetTokenAsync(user);
            var result = await _userManager.ResetPasswordAsync(user, resetToken, request.NewPassword);

            if (!result.Succeeded) {
                return new Response {
                    IsSuccess = false,
                    Message = "Failed to reset password.",
                    Data = result.Errors
                };
            }

            // Mark the reset token as used
            await _passwordResetService.MarkTokenAsUsedAsync(request.Email, request.ResetToken);

            // Send confirmation email
            var message = new Message(
                new[] { user.Email ?? string.Empty },
                "Password Reset Successful",
                "Your password has been successfully reset. If you did not request this change, please contact support immediately."
            );
            _emailService.SendEmail(message);

            return new Response {
                IsSuccess = true,
                Message = "Password reset successfully."
            };
        }

        public async Task<string> UploadFile(IFormFile file, string folderName) {
            if (file == null || file.Length == 0)
                return null;

            try {
                // Validate file size (limit to 5MB for profile images)
                const long maxFileSize = 5 * 1024 * 1024; // 5MB
                if (file.Length > maxFileSize) {
                    throw new InvalidOperationException("File size exceeds 5MB limit");
                }

                // Validate file type
                var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif" };
                if (!allowedTypes.Contains(file.ContentType.ToLower())) {
                    throw new InvalidOperationException("Only image files (JPEG, PNG, GIF) are allowed");
                }

                // Use S3 service to upload the file
                string key = await _s3Service.UploadFileAsync(file, folderName);

                if (string.IsNullOrEmpty(key)) {
                    throw new InvalidOperationException("Failed to upload file to storage");
                }

                // Return the key (path) of the file in S3
                return key;
            }
            catch (Exception ex) {
                // Log the exception with more details
                Console.WriteLine($"Error uploading file: {ex.Message}");
                throw new InvalidOperationException($"File upload failed: {ex.Message}", ex);
            }
        }

        public void DeleteFile(string filePath) {
            if (string.IsNullOrEmpty(filePath))
                return;

            try {
                // Use S3 service to delete the file
                _s3Service.DeleteFileAsync(filePath).Wait();
            }
            catch (Exception ex) {
                // Log the exception
            }
        }

        public async Task<Response> DeleteSQlUser() {
            // Implementation for DeleteSQlUser method
            // This method appears to be a placeholder or specific implementation
            return new Response {
                IsSuccess = true,
                Message = "SQL User deletion operation completed"
            };
        }
    }
}

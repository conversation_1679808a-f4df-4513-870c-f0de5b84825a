import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router,
} from '@angular/router';
import { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { UserProfileService } from '../Services/UserProfile.service';

@Injectable({
  providedIn: 'root',
})
export class AdminGuard implements CanActivate {
  constructor(
    private userProfileService: UserProfileService,
    private router: Router,
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.userProfileService.getUserRoles().pipe(
      take(1),
      map((roles) => {
        // Check if user has only "user" role (case-insensitive)
        const hasOnlyUserRole =
          roles.length === 1 && roles[0].toLowerCase() === 'user';

        // If user has only "user" role, deny access
        if (hasOnlyUserRole) {
          this.router.navigate(['/dashboard']);
          return false;
        }

        // Check for admin or super admin roles (case-insensitive)
        const hasAdminRole = roles.some(
          (role) =>
            role.toLowerCase() === 'admin' ||
            role.toLowerCase() === 'super admin' ||
            role.toLowerCase() === 'superadmin',
        );

        if (hasAdminRole) {
          return true;
        } else {
          this.router.navigate(['/dashboard']);
          return false;
        }
      }),
    );
  }
}

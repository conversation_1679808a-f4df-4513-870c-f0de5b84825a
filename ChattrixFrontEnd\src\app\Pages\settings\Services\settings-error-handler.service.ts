import { Injectable } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { NotificationService } from '../../../Core/Services/notification.service';

export interface SettingsErrorMapping {
  statusCode: number;
  message: string;
  userFriendlyMessage: string;
}

@Injectable({
  providedIn: 'root',
})
export class SettingsErrorHandlerService {
  constructor(private notificationService: NotificationService) {}

  handleSettingsError(error: HttpErrorResponse): void {
    const userMessage = this.mapErrorToUserMessage(error);
    this.notificationService.showError(userMessage);
  }

  private mapErrorToUserMessage(error: HttpErrorResponse): string {
    // Handle different status codes
    switch (error.status) {
      case 400:
        return this.handle400Error(error);
      case 401:
        return this.handle401Error(error);
      case 403:
        return this.handle403Error(error);
      case 404:
        return this.handle404Error(error);
      case 409:
        return this.handle409Error(error);
      case 500:
        return this.handle500Error(error);
      case 0:
        return this.handleNetworkError(error);
      default:
        return this.handleUnknownError(error);
    }
  }

  private handle400Error(error: HttpErrorResponse): string {
    const message = this.extractErrorMessage(error);
    const lowerMsg = message.toLowerCase();

    // Profile validation errors
    if (
      lowerMsg.includes('full name') ||
      lowerMsg.includes('name required') ||
      lowerMsg.includes('invalid name')
    ) {
      return 'Please enter a valid full name.';
    }

    if (
      lowerMsg.includes('phone number') ||
      lowerMsg.includes('invalid phone')
    ) {
      return 'Please enter a valid phone number.';
    }

    if (
      lowerMsg.includes('profile picture') ||
      lowerMsg.includes('image')
    ) {
      return 'Invalid profile picture. Please upload a valid image file.';
    }

    // Password change errors
    if (
      lowerMsg.includes('current password') ||
      lowerMsg.includes('old password')
    ) {
      if (lowerMsg.includes('incorrect') || lowerMsg.includes('invalid')) {
        return 'Current password is incorrect. Please try again.';
      }
      return 'Please enter your current password.';
    }

    if (
      lowerMsg.includes('new password') ||
      lowerMsg.includes('password')
    ) {
      if (lowerMsg.includes('weak') || lowerMsg.includes('requirements')) {
        return 'New password does not meet security requirements. Please use a stronger password.';
      }
      if (lowerMsg.includes('same') || lowerMsg.includes('current')) {
        return 'New password must be different from your current password.';
      }
      return 'Please enter a valid new password.';
    }

    if (
      lowerMsg.includes('confirm password') ||
      lowerMsg.includes('password confirmation')
    ) {
      return 'Password confirmation does not match. Please try again.';
    }

    // Generic validation error
    return message || 'Please check your input and try again.';
  }

  private handle401Error(error: HttpErrorResponse): string {
    const message = this.extractErrorMessage(error);
    const lowerMsg = message.toLowerCase();

    if (lowerMsg.includes('password') || lowerMsg.includes('authentication')) {
      return 'Authentication failed. Please log in again to continue.';
    }

    return 'You are not authorized to perform this action. Please log in again.';
  }

  private handle403Error(error: HttpErrorResponse): string {
    const message = this.extractErrorMessage(error);
    const lowerMsg = message.toLowerCase();

    if (lowerMsg.includes('profile') || lowerMsg.includes('settings')) {
      return 'You do not have permission to modify these settings. Please contact your administrator.';
    }

    return 'Access denied. You do not have permission to perform this action.';
  }

  private handle404Error(error: HttpErrorResponse): string {
    const message = this.extractErrorMessage(error);
    const lowerMsg = message.toLowerCase();

    if (lowerMsg.includes('user') || lowerMsg.includes('profile')) {
      return 'Profile not found. Please refresh the page and try again.';
    }

    return 'The requested resource was not found.';
  }

  private handle409Error(error: HttpErrorResponse): string {
    const message = this.extractErrorMessage(error);
    const lowerMsg = message.toLowerCase();

    if (lowerMsg.includes('email') || lowerMsg.includes('duplicate')) {
      return 'This email address is already in use. Please use a different email.';
    }

    if (lowerMsg.includes('conflict') || lowerMsg.includes('already exists')) {
      return 'This operation conflicts with existing data. Please check and try again.';
    }

    return message || 'A conflict occurred. Please try again.';
  }

  private handle500Error(error: HttpErrorResponse): string {
    const message = this.extractErrorMessage(error);
    const lowerMsg = message.toLowerCase();

    if (lowerMsg.includes('profile') || lowerMsg.includes('settings')) {
      return 'Server error occurred while updating your settings. Please try again later or contact support if the problem persists.';
    }

    return 'Server error occurred. Please try again later or contact support if the problem persists.';
  }

  private handleNetworkError(error: HttpErrorResponse): string {
    return 'Network connection error. Please check your internet connection and try again.';
  }

  private handleUnknownError(error: HttpErrorResponse): string {
    const message = this.extractErrorMessage(error);
    return message || 'An unexpected error occurred while updating your settings. Please try again.';
  }

  private extractErrorMessage(error: HttpErrorResponse): string {
    // Try different possible error message locations
    if (error.error?.message) {
      return error.error.message;
    }

    if (error.error?.error_description) {
      return error.error.error_description;
    }

    if (error.error?.Error) {
      return error.error.Error;
    }

    if (typeof error.error === 'string') {
      return error.error;
    }

    if (error.message) {
      return error.message;
    }

    return 'An error occurred';
  }

  showSuccess(message: string): void {
    this.notificationService.showSuccess(message);
  }

  showInfo(message: string): void {
    this.notificationService.showInfo(message);
  }
}

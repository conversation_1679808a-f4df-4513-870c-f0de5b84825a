import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, catchError, finalize, throwError } from 'rxjs';
import { environment } from '../../../../Environments/environment';
import { AuthStateService } from '../../authentication/Services/AuthState.service';
import { ApiResponse } from '../../authentication/Models';
import { SettingsErrorHandlerService } from './settings-error-handler.service';

// Profile update request interface
export interface ProfileUpdateRequest {
  fullName?: string;
  phoneNumber?: string;
  description?: string;
  profileImage?: File;
}

// Profile update response interface
export interface ProfileUpdateResponse extends ApiResponse<any> {}

@Injectable({
  providedIn: 'root',
})
export class ProfileUpdateService {
  private readonly API_URL = `${environment.apiUrl}/api/Account`;

  constructor(
    private httpClient: HttpClient,
    private authState: AuthStateService,
    private settingsErrorHandler: SettingsErrorHandlerService,
  ) {}

  updateProfile(data: ProfileUpdateRequest): Observable<ProfileUpdateResponse> {
    // Validate input data exists
    if (!data) {
      const errorMessage = 'Profile update data is required';
      this.authState.setError(errorMessage);
      return throwError(() => new Error(errorMessage));
    }

    // Check if at least one field is provided for update
    const hasFullName = data.fullName && data.fullName.trim().length > 0;
    const hasPhoneNumber =
      data.phoneNumber && data.phoneNumber.trim().length > 0;
    const hasDescription =
      data.description && data.description.trim().length > 0;
    const hasProfileImage = data.profileImage && data.profileImage.size > 0;

    if (
      !hasFullName &&
      !hasPhoneNumber &&
      !hasDescription &&
      !hasProfileImage
    ) {
      const errorMessage = 'At least one field must be provided for update';
      this.authState.setError(errorMessage);
      return throwError(() => new Error(errorMessage));
    }

    // Validate full name
    if (hasFullName && data.fullName!.trim().length < 2) {
      const errorMessage = 'Full name must be at least 2 characters long';
      this.authState.setError(errorMessage);
      return throwError(() => new Error(errorMessage));
    }

    // Validate full name length
    if (hasFullName && data.fullName!.trim().length > 100) {
      const errorMessage = 'Full name must be less than 100 characters';
      this.authState.setError(errorMessage);
      return throwError(() => new Error(errorMessage));
    }

    // Validate phone number
    if (hasPhoneNumber) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
      if (!phoneRegex.test(data.phoneNumber!.replace(/[\s\-\(\)]/g, ''))) {
        const errorMessage = 'Please enter a valid phone number';
        this.authState.setError(errorMessage);
        return throwError(() => new Error(errorMessage));
      }
    }

    // Validate description length
    if (hasDescription && data.description!.trim().length > 50) {
      const errorMessage = 'Description must be less than 50 characters';
      this.authState.setError(errorMessage);
      return throwError(() => new Error(errorMessage));
    }

    // Validate profile image
    if (hasProfileImage) {
      const maxFileSize = 5 * 1024 * 1024; // 5MB
      if (data.profileImage!.size > maxFileSize) {
        const errorMessage = 'Profile image must be less than 5MB';
        this.authState.setError(errorMessage);
        return throwError(() => new Error(errorMessage));
      }

      const allowedTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
      ];
      if (!allowedTypes.includes(data.profileImage!.type.toLowerCase())) {
        const errorMessage = 'Only image files (JPEG, PNG, GIF) are allowed';
        this.authState.setError(errorMessage);
        return throwError(() => new Error(errorMessage));
      }
    }

    this.authState.setLoading(true);
    this.authState.clearError();

    // Create FormData for file upload support
    const formData = new FormData();

    // Use PascalCase field names to match backend UpdateProfileRequest model
    if (hasFullName) {
      formData.append('FullName', data.fullName!.trim());
    }

    if (hasPhoneNumber) {
      formData.append('PhoneNumber', data.phoneNumber!.trim());
    }

    if (hasDescription) {
      formData.append('Description', data.description!.trim());
    }

    if (hasProfileImage) {
      formData.append('ProfileImage', data.profileImage!);
    }

    return this.httpClient
      .put<ProfileUpdateResponse>(`${this.API_URL}/UpdateProfile`, formData)
      .pipe(
        catchError((error) => {
          this.settingsErrorHandler.handleSettingsError(error);
          return throwError(() => error);
        }),
        finalize(() => {
          this.authState.setLoading(false);
        }),
      );
  }

  getCurrentProfile(): Observable<ApiResponse<any>> {
    this.authState.setLoading(true);
    this.authState.clearError();

    return this.httpClient
      .get<ApiResponse<any>>(`${this.API_URL}/GetCurrentUser`)
      .pipe(
        catchError((error) => {
          this.settingsErrorHandler.handleSettingsError(error);
          return throwError(() => error);
        }),
        finalize(() => {
          this.authState.setLoading(false);
        }),
      );
  }
}
